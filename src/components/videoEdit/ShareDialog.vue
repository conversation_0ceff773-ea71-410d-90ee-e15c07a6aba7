<template>
  <div class="share-dialog-overlay" v-if="visible" @click.self="handleClose">
    <div class="share-dialog" @click.stop>
      <!-- 视频封面背景 -->
      <div class="video-cover-background">
        <img 
          v-if="video && video.videoUrl" 
          :src="`${video.videoUrl}?x-oss-process=video/snapshot,t_0,f_jpg`" 
          alt="视频封面"
          class="cover-image"
        />
        <div class="cover-overlay"></div>
      </div>

      <!-- 分享内容区域 -->
      <div class="share-content">
        <!-- 关闭按钮 -->
        <button class="close-button" @click="handleClose">
          <el-icon><Close /></el-icon>
        </button>

        <!-- 分享标题 -->
        <div class="share-header">
          <h2 class="share-title">分享视频</h2>
          <p class="share-subtitle">将您的精彩创作分享给更多人</p>
        </div>

        <!-- 视频信息卡片 -->
        <div class="video-info-card">
          <div class="video-thumbnail">
            <img 
              v-if="video && video.videoUrl" 
              :src="`${video.videoUrl}?x-oss-process=video/snapshot,t_0,f_jpg`" 
              alt="视频缩略图"
            />
            <div class="play-overlay">
              <el-icon class="play-icon"><VideoPlay /></el-icon>
            </div>
            <div class="duration-badge" v-if="video && video.videoDuration">
              {{ formatDuration(video.videoDuration) }}
            </div>
          </div>
          <div class="video-details">
            <div class="video-meta">
              <span class="create-time">{{ formatDate(video?.createTime) }}</span>
              <span class="resolution">{{ video?.resolution || '1080p' }}</span>
            </div>
          </div>
        </div>

        <!-- 分享操作按钮 -->
        <div class="share-actions">
          <div v-if="video?.shareStatus === 1 && video?.shareUrl" class="shared-status">
            <div class="share-link-display">
              <el-icon class="link-icon"><Link /></el-icon>
              <span class="link-text">{{ truncateUrl(video.shareUrl) }}</span>
              <button class="copy-button" @click="copyLink">
                <el-icon><DocumentCopy /></el-icon>
                复制链接
              </button>
            </div>
            <button class="unshare-button" @click="handleUnshare">
              取消分享
            </button>
          </div>
          
          <div v-else class="share-buttons">
            <button class="primary-share-button" @click="handleShare">
              <el-icon><Share /></el-icon>
              创建分享链接
            </button>
          </div>
        </div>

        <!-- 分享提示 -->
        <div class="share-tips">
          <div class="tip-item">
            <el-icon class="tip-icon"><InfoFilled /></el-icon>
            <span>分享链接永久有效，任何人都可以通过链接观看您的视频</span>
          </div>
          <div class="tip-item">
            <el-icon class="tip-icon"><Lock /></el-icon>
            <span>您可以随时取消分享，取消后链接将失效</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { ElMessage } from 'element-plus';
import { 
  Close, 
  VideoPlay, 
  Link, 
  Share, 
  DocumentCopy, 
  InfoFilled, 
  Lock 
} from '@element-plus/icons-vue';

// 组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  video: {
    type: Object,
    default: null
  }
});

// 事件
const emit = defineEmits(['close', 'share']);

// 关闭弹框
const handleClose = () => {
  emit('close');
};

// 处理分享
const handleShare = () => {
  emit('share', 'share');
};

// 处理取消分享
const handleUnshare = () => {
  emit('share', 'unshare');
};

// 复制链接
const copyLink = () => {
  if (!props.video?.shareUrl) {
    ElMessage.error('分享链接不存在');
    return;
  }

  navigator.clipboard.writeText(props.video.shareUrl)
    .then(() => {
      ElMessage.success('分享链接已复制到剪贴板');
    })
    .catch(err => {
      console.error('复制失败:', err);
      ElMessage.error('复制链接失败，请手动复制');
    });
};

// 格式化日期
const formatDate = (timestamp) => {
  if (!timestamp) return '';
  const date = new Date(timestamp);
  if (date.getFullYear() === new Date().getFullYear()) {
    return `${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
  } else {
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
  }
};

// 格式化视频时长
const formatDuration = (milliseconds) => {
  if (!milliseconds) return '00:00';
  const seconds = milliseconds / 1000;
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${String(mins).padStart(2, '0')}:${String(secs).padStart(2, '0')}`;
};

// 截断URL显示
const truncateUrl = (url) => {
  if (!url) return '';
  if (url.length <= 40) return url;
  return url.substring(0, 20) + '...' + url.substring(url.length - 17);
};
</script>

<style scoped>
/* 弹框遮罩层 */
.share-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 3000;
  backdrop-filter: blur(8px);
  animation: fadeIn 0.3s ease-out;
}

/* 弹框主体 */
.share-dialog {
  position: relative;
  width: 90vw;
  max-width: 600px;
  height: 80vh;
  max-height: 500px;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: slideUp 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

/* 视频封面背景 */
.video-cover-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: blur(2px) brightness(0.7);
}

.cover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.6) 0%,
    rgba(0, 0, 0, 0.3) 50%,
    rgba(0, 0, 0, 0.7) 100%
  );
}

/* 分享内容区域 */
.share-content {
  position: relative;
  z-index: 2;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 32px;
  color: white;
}

/* 关闭按钮 */
.close-button {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 40px;
  height: 40px;
  border: none;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

/* 分享标题 */
.share-header {
  text-align: center;
  margin-bottom: 40px;
}

.share-title {
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 8px 0;
  background: linear-gradient(135deg, #ffffff, #e2e8f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.share-subtitle {
  font-size: 16px;
  opacity: 0.8;
  margin: 0;
}

/* 视频信息卡片 */
.video-info-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 32px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  gap: 16px;
  align-items: center;
}

.video-thumbnail {
  position: relative;
  width: 120px;
  height: 68px;
  border-radius: 12px;
  overflow: hidden;
  flex-shrink: 0;
}

.video-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.play-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #333;
}

.duration-badge {
  position: absolute;
  bottom: 6px;
  right: 6px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.video-details {
  flex: 1;
}

.video-meta {
  display: flex;
  gap: 16px;
  font-size: 14px;
  opacity: 0.9;
}

/* 分享操作区域 */
.share-actions {
  margin-bottom: 32px;
}

.shared-status {
  text-align: center;
}

.share-link-display {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  backdrop-filter: blur(10px);
}

.link-icon {
  color: #10b981;
  font-size: 20px;
}

.link-text {
  flex: 1;
  font-family: monospace;
  font-size: 14px;
  opacity: 0.9;
}

.copy-button {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.copy-button:hover {
  background: rgba(255, 255, 255, 0.3);
}

.unshare-button {
  background: rgba(239, 68, 68, 0.2);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: #fca5a5;
  padding: 12px 24px;
  border-radius: 12px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.unshare-button:hover {
  background: rgba(239, 68, 68, 0.3);
  color: white;
}

.share-buttons {
  text-align: center;
}

.primary-share-button {
  background: linear-gradient(135deg, #6366f1, #4f46e5);
  border: none;
  color: white;
  padding: 16px 32px;
  border-radius: 16px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
}

.primary-share-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(99, 102, 241, 0.4);
}

/* 分享提示 */
.share-tips {
  margin-top: auto;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.tip-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 14px;
  opacity: 0.8;
}

.tip-icon {
  color: #10b981;
  font-size: 16px;
  margin-top: 1px;
  flex-shrink: 0;
}

/* 动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .share-dialog {
    width: 95vw;
    height: 85vh;
  }
  
  .share-content {
    padding: 24px;
  }
  
  .share-title {
    font-size: 24px;
  }
  
  .video-info-card {
    flex-direction: column;
    text-align: center;
  }
  
  .video-thumbnail {
    width: 160px;
    height: 90px;
  }
}

/* 暗色主题支持 */
body.dark .share-dialog {
  background: rgba(30, 30, 45, 0.95);
}

body.dark .video-info-card {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.05);
}

body.dark .share-link-display {
  background: rgba(255, 255, 255, 0.05);
}
</style>
