<template>
  <div class="video-generation-panel">
    <!-- 添加标签页 -->
    <el-tabs v-model="activeTab" class="generation-tabs asset-selector-tabs" @tab-click="handleTabChange">
      <el-tab-pane label="图片生成" name="image"></el-tab-pane>
      <el-tab-pane label="视频生成" name="video"></el-tab-pane>
      <el-tab-pane label="音频生成" name="audio"></el-tab-pane>
    </el-tabs>

    <div class="shot-label" v-if="shotData.id">
      分镜 {{ shotData.sortOrder }}
    </div>
    <div class="shot-label" v-else>
      创建空白分镜
    </div>

    <!-- 内容区域 -->
    <div class="generation-content">
      <!-- 图片生成 tab -->
      <div v-if="activeTab === 'image'" class="tab-content">
        <ImageGenerationPanel
          :shot="shotData"
          :canvas-id="canvasId"
          @update:shot="handleShotUpdate"
          @refresh-canvas="emit('refresh-canvas')"
        />
      </div>

      <!-- 视频生成 tab -->
      <div v-if="activeTab === 'video'" class="tab-content">
        <VideoGenerationPanelChild
          :shot="shotData"
          :canvas-id="canvasId"
          @update:shot="handleShotUpdate"
          @refresh-canvas="emit('refresh-canvas')"
        />
      </div>

      <!-- 音频生成 tab -->
      <div v-if="activeTab === 'audio'" class="tab-content">
        <AudioGenerationPanel
          :shot="shotData"
          :voices="voices"
          :isLoadingVoices="isLoadingVoices"
          @refresh-canvas="emit('refresh-canvas')"
          @refresh-voices="refreshVoices"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onBeforeUnmount, onMounted } from 'vue';

import { ElMessage } from 'element-plus';

import AudioGenerationPanel from './AudioGenerationPanel.vue';
import ImageGenerationPanel from './ImageGenerationPanel.vue';
import VideoGenerationPanelChild from './VideoGenerationPanelChild.vue';

// 组件属性
const props = defineProps({
  shot: {
    type: Object,
    required: true
  },
  voices: {
    type: Array,
    default: () => []
  },
  isLoadingVoices: {
    type: Boolean,
    default: false
  },
  activeTabFromParent: {
    type: String,
    default: 'video'
  },
  canvasId: {
    type: [Number, String],
    default: ''
  }
});

// 事件
const emit = defineEmits(['update:shot', 'refresh-voices', 'tab-change', 'refresh-canvas']);

// 本地数据副本
const shotData = ref({ ...props.shot });

// 当前活动的tab
const activeTab = ref(props.activeTabFromParent || 'video');

onMounted(() => {
  // 确保shotData存在
  if (!shotData.value) {
    console.warn('VideoGenerationPanel: shotData is null during mount');
    return;
  }

  if (shotData.value.startFrameImage === '' || shotData.value.startFrameImage === null) {
    shotData.value.startFrameImage = shotData.value.imageUrl;
  }

  // if (shotData.value.referenceImage === '' || shotData.value.referenceImage === null) {
  //   shotData.value.referenceImage = shotData.value.imageUrl;
  // }
  shotData.value.referenceImage = "";

  if (shotData.value.videoPrompt === '' || shotData.value.videoPrompt === null) {
    shotData.value.videoPrompt = shotData.value.videoConvertPrompt;
  }
});

// 监听外部shot变化
watch(() => props.shot, (newShot) => {
  if (!newShot) return;

  shotData.value = { ...newShot };

  if (shotData.value.startFrameImage === '' || shotData.value.startFrameImage === null) {
    shotData.value.startFrameImage = shotData.value.imageUrl;
  }

  // if (shotData.value.referenceImage === '' || shotData.value.referenceImage === null) {
  //   shotData.value.referenceImage = shotData.value.imageUrl;
  // }

  if (shotData.value.videoPrompt === '' || shotData.value.videoPrompt === null) {
    shotData.value.videoPrompt = shotData.value.videoConvertPrompt;
  }
}, { deep: true });

// 监听父组件传入的activeTabFromParent变化
watch(() => props.activeTabFromParent, (newTab) => {
  console.log('newTab', newTab);
  if (newTab && ['image', 'video', 'audio'].includes(newTab)) {
    activeTab.value = newTab;
  }
});

// 处理子组件的shot更新
const handleShotUpdate = (updatedShot) => {
  shotData.value = { ...updatedShot };
  emit('update:shot', updatedShot);
};



// 刷新音色列表（在实际项目中，这里应该调用API获取最新的音色列表）
const refreshVoices = () => {
  // 触发事件，让父组件刷新音色列表
  emit('refresh-voices');
};

// 处理标签页点击事件
const handleTabChange = (tab) => {
  // 处理标签页点击逻辑
  console.log(`Tab ${tab.name} clicked`);
  // 通知父组件标签页已变更，确保传递正确的 tab 名称
  emit('tab-change', tab.name);
  activeTab.value = tab.name;
};
</script>

<style scoped>
.shot-label {
  /* width: fit-content; */
  padding: 6px 16px;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  background-color: #f5f7fa;
  border-radius: 6px;
  margin: 10px 16px;
}

body.dark .shot-label {
  background-color: rgba(204, 221, 255, .06);
}

.video-generation-panel {
  height: 100%;
  /* padding: 0 5px; */
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.generation-content {
  flex: 1;
  /* overflow-y: auto; */
}

.tab-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 12px;
}

.video-image-content {
  display: flex;
  flex-direction: row;
  gap: 10px;
}

.video-image-content .flex-1 {
  flex: 1;
}

.panel-section {
  /* margin-bottom: 15px; */
}

.mt-10 {
  margin-top: auto;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.mt-11 {
  margin-top: 0px;
}

.section-title {
  font-size: 15px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 15px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
}

.image-generation-textarea {
  background-color: #f5f7fa;
  border-radius: 8px;
  box-shadow: 0 0 1px 0 rgba(0, 0, 0, 0.1) !important;
  padding-bottom: 14px;
}

body.dark .image-generation-textarea {
  background-color: rgba(204, 221, 255, .06);
  box-shadow: 0 0 1px 0 rgba(0, 0, 0, 0.1) !important;
}

body.dark .section-title {
  color: var(--text-primary);
  border-bottom-color: var(--border-color);
}

.form-item {
  margin-bottom: 0px;
  position: relative;
}

.form-label {
  font-size: 12px;
  font-weight: 500;
  color: #606266;
  margin-bottom: 5px;
  text-align: left;
}

body.dark .form-label {
  color: var(--text-primary);
}

.panel-actions {
  margin-top: auto;
  padding: 0;
  display: flex;
  justify-content: center;
}

.panel-action-audio {
  padding: 0;
  display: flex;
  justify-content: center;
}

/* 生成按钮样式 - 参考 VideoEditorHeader.vue 的 back-button */
.generation-button {
  width: 100%;
  justify-content: center;
  padding: 6px 12px;
  font-size: 15px;
}

.points-cost {
  margin-left: 5px;
  font-size: 14px;
  opacity: 0.9;
}

/* 返回按钮 */
.back-button {
  display: flex;
  align-items: center;
  gap: 5px;
  cursor: pointer;
  font-size: 14px;
  color: #ffffff;
  transition: color 0.3s;
  background-color: #706cefd7;
  padding: 8px 12px;
  border-radius: 18px;
}

.back-button:hover {
  background-color: #5855e9d7;
}

.back-button.is-generating {
  background-color: #5855e9d7;
  cursor: not-allowed;
  position: relative;
}

.loading-icon {
  animation: rotating 2s linear infinite;
}

.circular-icon {
  height: 16px;
  width: 16px;
  animation: rotating 2s linear infinite;
}

.path {
  stroke: white;
  stroke-width: 3;
  stroke-dasharray: 90, 150;
  stroke-dashoffset: 0;
  stroke-linecap: round;
  animation: dash 1.5s ease-in-out infinite;
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

@keyframes dash {
  0% {
    stroke-dasharray: 1, 150;
    stroke-dashoffset: 0;
  }

  50% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -35;
  }

  100% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -124;
  }
}

body.dark .back-button {
  background-color: rgba(112, 108, 239, 0.7);
}

body.dark .back-button:hover {
  background-color: rgba(88, 85, 233, 0.7);
}







/* Element Plus 组件样式覆盖 */
:deep(.el-textarea__inner) {
  font-size: 13px;
}

:deep(.el-input__inner) {
  font-size: 13px;
}

:deep(.el-slider__runway) {
  margin: 8px 0;
}

:deep(.el-slider__input) {
  width: 60px;
  margin-left: 10px;
}

/* 滚动条样式 */
.video-generation-panel::-webkit-scrollbar,
.generation-content::-webkit-scrollbar {
  width: 6px;
}

.video-generation-panel::-webkit-scrollbar-thumb,
.generation-content::-webkit-scrollbar-thumb {
  background-color: #c0c4cc;
  border-radius: 3px;
}

.video-generation-panel::-webkit-scrollbar-track,
.generation-content::-webkit-scrollbar-track {
  background-color: #f5f7fa;
}

body.dark .video-generation-panel::-webkit-scrollbar-thumb,
body.dark .generation-content::-webkit-scrollbar-thumb {
  background-color: #606266;
}

body.dark .video-generation-panel::-webkit-scrollbar-track,
body.dark .generation-content::-webkit-scrollbar-track {
  background-color: var(--bg-secondary);
}

/* Tab 样式 - 从 Assets.vue 复制并调整 */
.generation-tabs {
  /* padding: 10px 10px 0 10px; */
  padding: 6px 0 0 0;
  border-bottom: 1px solid #8585852a;
}

.asset-selector-tabs :deep(.el-tabs__header) {
  position: relative !important;
  margin: 0 !important;
}

.asset-selector-tabs :deep(.el-tabs__item) {
  color: #606266 !important;
  font-size: 14px !important;
  padding: 0 16px !important;
  height: 38px !important;
  line-height: 38px !important;
  transition: all 0.3s !important;
}

body.dark .asset-selector-tabs :deep(.el-tabs__item) {
  color: var(--text-secondary) !important;
}

.asset-selector-tabs :deep(.el-tabs__item.is-active) {
  color: #4f46e5 !important;
  font-weight: 500 !important;
}

body.dark .asset-selector-tabs :deep(.el-tabs__item.is-active) {
  color: #6366f1 !important;
}

.asset-selector-tabs :deep(.el-tabs__active-bar) {
  background-color: transparent !important;
  background-image: linear-gradient(90deg, transparent 0, transparent 0%,
      #4f46e5 0, #4f46e5 100%,
      transparent 0, transparent) !important;
  height: 2px !important;
}

body.dark .asset-selector-tabs :deep(.el-tabs__active-bar) {
  background-image: linear-gradient(90deg, transparent 0, transparent 0%,
      #6366f1 0, #6366f1 100%,
      transparent 0, transparent) !important;
}

.asset-selector-tabs :deep(.el-tabs__nav-wrap::after) {
  position: static !important;
  height: 1px !important;
  background-color: #e4e7ed !important;
}

body.dark .asset-selector-tabs :deep(.el-tabs__nav-wrap::after) {
  background-color: var(--border-color) !important;
}

.asset-selector-tabs :deep(.el-tabs__nav) {
  height: auto !important;
}


</style>