<template>
  <header class="video-editor-header">
    <!-- 左侧：返回按钮和项目下拉 -->
    <div class="header-left">
      <!-- 返回按钮 -->
      <!-- <div class="back-button" @click="goBack">
        <el-icon>
          <ArrowLeft />
        </el-icon>
        <span class="back-button-text">返回</span>
      </div> -->

      <img src="../../assets/logo.svg" alt="Logo" class="logo-img"  @click="goBack"/>
      
      <!-- 项目下拉选择器 - 使用 DropdownPanel 替换 el-dropdown -->
      <DropdownPanel 
        v-model="showProjectDropdown" 
        position="bottom"
        align="start"
        closeOnClickOutside
        :zIndex="1200"
      >
        <!-- 触发器插槽 -->
        <template #trigger>
          <div class="project-selector">
            <div class="project-name-container" @click="startEditProjectName" v-if="!isEditingProjectName">
              <span class="project-name">{{ currentProject.name }}</span>
            </div>
            <el-input
              v-else
              ref="projectNameInput"
              v-model="editingProjectName"
              size="small"
              @blur="saveProjectName"
              @keyup.enter="saveProjectName"
              @keyup.esc="cancelEditProjectName"
              class="project-name-input"
            />
            <el-icon class="el-icon--right" @click.stop="toggleProjectDropdown">
              <ArrowDown />
            </el-icon>
          </div>
        </template>
        
        <!-- 下拉内容插槽 -->
        <div class="project-dropdown-menu">
          <div 
            v-for="project in projects" 
            :key="project.id" 
            class="project-dropdown-item"
            @click="handleProjectChange(project.id)"
          >
            {{ project.name }}
          </div>
          <div class="project-dropdown-item project-dropdown-item-new" @click="handleProjectChange('new')">
            <el-icon><Plus /></el-icon> 新建项目
          </div>
        </div>
      </DropdownPanel>
    </div>
    
    <!-- 中间：比例选择器 -->
    <div class="header-center">
      <DropdownPanel 
        v-model="showRatioSelector" 
        position="bottom"
        align="center"
        closeOnClickOutside
        :width="300"
        :zIndex="1200"
        :maxHeight="500"
      >
        <!-- 触发器插槽 -->
        <template #trigger>
          <div class="current-ratio">
            <span class="current-ratio-text">{{ aspectRatio }}</span>
            <!-- <span class="current-ratio-desc">{{ getRatioDescription(aspectRatio) }}</span> -->
            <el-icon class="expand-icon" :class="{ 'rotate': showRatioSelector }"><ArrowDown /></el-icon>
          </div>
        </template>
        
        <!-- 下拉内容插槽 -->
        <RatioSelector 
          v-model="aspectRatio" 
          @update:modelValue="handleRatioChange"
        />
      </DropdownPanel>
    </div>
    
    <!-- 右侧：积分显示和导出按钮 -->
    <div class="header-right">

      <div class="nav-buttons">
        <!-- <el-button class="nav-button" type="text" @click="goToStories">故事</el-button> -->
        <!-- <el-button class="nav-button" type="primary" @click="goToCreate">创建</el-button> -->
        <ThemeSwitch :is-transparent="transparent" />
      </div>

      <!-- 积分显示 -->
      <div class="points-display" @click="goToProfile">
        <el-icon>
          <Coin />
        </el-icon>
        <span class="points-value">{{ formatPoints(userPoints) }}</span>
      </div>
      
      <!-- 导出按钮 - 使用 DropdownPanel -->
      <DropdownPanel
        v-model="exportDialogVisible"
        position="bottom"
        align="end"
        closeOnClickOutside
        :width="300"
        :maxHeight="600"
        :zIndex="1300"
      >
        <!-- 触发器插槽 -->
        <template #trigger>
          <div class="back-button">
            <el-icon><Download /></el-icon>
            <span class="back-button-text">导出</span>
          </div>
        </template>

        <!-- 下拉内容插槽 -->
        <ExportVideoContent
          :default-resolution="exportResolution"
          :is-exporting="isExporting"
          :canvas-id="canvasId"
          @video-click="handleVideoClick"
          @export="doExport"
          @update:resolution="setExportResolution"
          @update:isExporting="(val) => isExporting = val"
          @close="exportDialogVisible = false"
        />
      </DropdownPanel>
    </div>
  </header>
</template>

<script setup>
import { ref, computed, inject, nextTick, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import { ArrowLeft, ArrowDown, Coin, Download, Plus } from '@element-plus/icons-vue';
import ThemeSwitch from '@/components/selector/ThemeSwitch.vue'
import { ElMessage } from 'element-plus';
import RatioSelector from '../selector/RatioSelector.vue';
import DropdownPanel from '../parent/DropdownPanel.vue';
import { getCanvasList, updateCanvas } from '@/api/auth.js';
import ExportVideoContent from './ExportVideoContent.vue';

// 组件属性
const props = defineProps({
  defaultAspectRatio: {
    type: String,
    default: '16:9'
  },
  canvasName: {
    type: String,
    default: ''
  },
  canvasId: {
    type: [String, Number],
    default: ''
  }
});

// 事件
const emit = defineEmits(['aspect-ratio-change', 'export', 'project-change', 'project-switch']);

// 路由
const router = useRouter();

// 从App.vue注入积分相关的变量和函数
const userPoints = inject('userPoints', 0);
const formatNumber = inject('formatNumber', (num) => num?.toString() || '0');

// 格式化积分显示
const formatPoints = (points) => {
  return formatNumber(points);
};

// 返回上一页
const goBack = () => {
  // 如果没有上一页，则跳转到首页
  if (window.history.length <= 1) {
    router.push('/home');
  } else {
    router.back();
  }
};

// 跳转到个人中心
const goToProfile = () => {
  router.push('/profile');
};

// 比例选择器状态
const aspectRatio = computed({
  get: () => props.defaultAspectRatio,
  set: (value) => emit('aspect-ratio-change', value)
});
const showRatioSelector = ref(false);

// 主题相关状态
const transparent = ref(false);

// 项目列表（从API获取）
const projects = ref([]);
const isLoadingProjects = ref(false);

// 当前选中的项目
const currentProject = computed(() => {
  if (props.canvasId && projects.value.length > 0) {
    return projects.value.find(p => p.id === props.canvasId) || {
      id: props.canvasId,
      name: props.canvasName || '当前项目'
    };
  }
  return { id: '', name: props.canvasName || '选择项目' };
});

// 项目下拉状态
const showProjectDropdown = ref(false);

// 项目名称编辑状态
const isEditingProjectName = ref(false);
const editingProjectName = ref('');
const projectNameInput = ref(null);

// 切换项目下拉
const toggleProjectDropdown = () => {
  showProjectDropdown.value = !showProjectDropdown.value;
};

// 开始编辑项目名称
const startEditProjectName = () => {
  if (!props.canvasId || !currentProject.value) return;

  isEditingProjectName.value = true;
  editingProjectName.value = currentProject.value.name;

  // 使用 nextTick 确保 DOM 更新后再聚焦输入框
  nextTick(() => {
    if (projectNameInput.value) {
      projectNameInput.value.focus();
    }
  });
};

// 保存项目名称
const saveProjectName = async () => {
  if (isEditingProjectName.value && props.canvasId) {
    const newName = editingProjectName.value.trim();
    if (!newName) {
      isEditingProjectName.value = false;
      return;
    }

    try {
      // 调用 updateCanvas API 更新画布名称
      const response = await updateCanvas({
        canvasId: props.canvasId,
        canvasName: newName
      });

      if (response.success) {
        // 通知父组件项目名称已更改
        emit('project-change', { id: props.canvasId, name: newName });
        // 更新项目列表中的项目名称
        await fetchCanvasList();
        // ElMessage.success('项目名称更新成功');
      } else {
        ElMessage.error(`更新项目名称失败: ${response.errMessage}`);
      }
    } catch (error) {
      console.error('更新项目名称异常:', error);
      ElMessage.error('更新项目名称出错，请稍后再试');
    }
  }

  // 退出编辑模式
  isEditingProjectName.value = false;
};

// 取消编辑项目名称
const cancelEditProjectName = () => {
  isEditingProjectName.value = false;
};

// 处理比例变化
const handleRatioChange = (newRatio) => {
  showRatioSelector.value = false;
  aspectRatio.value = newRatio; // 使用计算属性的 setter，它会自动触发 emit
};

// 处理项目变更
const handleProjectChange = (projectId) => {
  if (projectId === 'new') {
    // 处理新建项目
    ElMessage.info('新建项目功能待实现');
  } else {
    // 切换到选中的项目
    const project = projects.value.find(p => p.id === projectId);
    if (project && project.id !== props.canvasId) {
      // 更新路由参数
      router.push({
        path: '/video-editor',
        query: { canvasId: project.id }
      });

      // 通知父组件切换项目
      emit('project-switch', project.id);
    }
  }

  // 关闭下拉菜单
  showProjectDropdown.value = false;
};

// 导出弹框相关状态
const exportDialogVisible = ref(false);
const exportResolution = ref('1080p'); // 默认选择720p分辨率
const isExporting = ref(false); // 导出中状态



// 获取画布列表
const fetchCanvasList = async () => {
  try {
    isLoadingProjects.value = true;
    const params = {
      pageNum: 1,
      pageSize: 20
    };

    const response = await getCanvasList(params);
    if (response.success) {
      projects.value = response.data.map(item => ({
        id: item.id,
        name: item.canvasName,
        code: item.code,
        description: item.canvasDesc,
        createTime: item.createTime
      }));
      console.log('获取画布列表成功:', projects.value.length);
    } else {
      console.error('获取画布列表失败:', response.errMessage);
    }
  } catch (error) {
    console.error('获取画布列表异常:', error);
  } finally {
    isLoadingProjects.value = false;
  }
};

// 组件挂载时获取画布列表
onMounted(() => {
  fetchCanvasList();
  
  // 监听积分更新事件
  window.addEventListener('user-points-updated', handlePointsUpdated);
});

// 处理积分更新事件
const handlePointsUpdated = (event) => {
  if (event.detail && event.detail.points !== undefined) {
    console.log('VideoEditorHeader: 积分已更新:', event.detail.points);
    // 不需要手动更新 userPoints，因为它是通过 inject 注入的响应式变量
  }
};

// 组件卸载前清理事件监听器
onUnmounted(() => {
  window.removeEventListener('user-points-updated', handlePointsUpdated);
});

// 处理视频点击预览
const handleVideoClick = (video) => {
  if (video.videoUrl) {
    // 使用全局视频预览函数
    if (window.openVideoPreview && typeof window.openVideoPreview === 'function') {
      window.openVideoPreview(
        video.videoUrl,
        video.prompt || '',
        {
          title: video.prompt ? `视频预览` : '',
          resolution: video.resolution || '',
          duration: video.videoDuration || 0
        }
      );
    } else {
      // 打开新窗口预览视频
      window.open(video.videoUrl, '_blank');
    }
  }
};

// 设置导出分辨率
const setExportResolution = (resolution) => {
  exportResolution.value = resolution;
};

// 执行导出
const doExport = (exportData) => {
  if (exportData.success) {
    ElMessage.success(`视频导出任务已提交，请稍后查看`);
    // 关闭导出弹框
    // exportDialogVisible.value = false;
  } else {
    ElMessage.error(`视频导出失败: ${exportData.error || '未知错误'}`);
  }
  
  // 通知父组件导出完成
  emit('export', {
    resolution: exportData.resolution,
    showSubtitles: exportData.showSubtitles,
    success: exportData.success,
    data: exportData.data
  });
};
</script>

<style scoped>

.nav-buttons {
  display: flex;
  align-items: center;
  gap: 16px;
}

.logo-img{
  width: 28px;
  height: 28px;
  cursor: pointer;
}
.video-editor-header {
  width: 100%;
  /* height: 50px; */
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 10px;
  /* background-color: #ffffff7c; */
  box-sizing: border-box;
  border-radius: 8px;
  /* box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.2); */
  /* margin: 6px; */
}

/* 左侧区域 */
.header-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* 返回按钮 */
.back-button {
  display: flex;
  align-items: center;
  gap: 5px;
  cursor: pointer;
  font-size: 14px;
  color: #ffffff;
  transition: color 0.3s;
  background-color: #706cefd7;
  padding: 4px 12px;
  border-radius: 8px;
}

.back-button:hover {
  background-color: #5855e9d7;
}

body.dark .back-button {
  background-color: rgba(112, 108, 239, 0.7);
}

body.dark .back-button:hover {
  background-color: rgba(88, 85, 233, 0.7);
}

/* 项目选择器 */
.project-selector {
  display: flex;
  align-items: center;
  padding: 3px 12px;
  border-radius: 8px;
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
  cursor: pointer;
  transition: all 0.3s;
  color: #878787;
}

.project-selector:hover {
  border-color: #c0c4cc;
  background-color: #f0f2f5;
}

body.dark .project-selector {
  background-color: var(--bg-tertiary, #2a2a3c);
  border-color: var(--border-color, #3e3e5e);
  color: var(--text-primary);
}

body.dark .project-selector:hover {
  background-color: var(--bg-hover, #323248);
}

.project-name-container {
  cursor: text;
  flex: 1;
}

.project-name {
  font-size: 14px;
  margin-right: 8px;
  font-weight: 500;
}

.project-name-input {
  margin-right: 8px;
}

.project-name-input :deep(.el-input__wrapper) {
  padding: 0 8px;
  box-shadow: none !important;
  background: transparent;
}

.project-name-input :deep(.el-input__inner) {
  height: 24px;
  font-size: 14px;
  font-weight: 500;
}

/* 项目下拉菜单 */
.project-dropdown-menu {
  width: 100%;
  padding: 6px 0;
}

.project-dropdown-item {
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 6px;
  max-lines: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: left;
}

.project-dropdown-item:hover {
  background-color: #f5f7fa;
}

body.dark .project-dropdown-item:hover {
  background-color: var(--bg-hover, #323248);
}

.project-dropdown-item-new {
  border-top: 1px solid #e4e7ed;
  margin-top: 6px;
  padding-top: 8px;
  color: #6366f1;
}

body.dark .project-dropdown-item-new {
  border-top-color: var(--border-color, #3e3e5e);
}

/* 中间区域 */
.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 20px;
}

/* 当前尺寸显示样式 */
.current-ratio {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 2px 8px;
  border-radius: 8px;
  /* border: 1px solid #e5e7eb; */
  background-color: #fff;
  cursor: pointer;
  transition: all 0.2s ease;
  /* gap: 10px; */
}

body.dark .current-ratio {
  background-color: var(--bg-secondary, #1e1e2d);
  border-color: var(--border-color, #2d2d3f);
}

.current-ratio:hover {
  border-color: #a5b4fc;
  background-color: #f9faff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

body.dark .current-ratio:hover {
  border-color: #818cf8;
  background-color: var(--bg-hover, #2a2a3c);
}

.current-ratio-text {
  font-size: 16px;
  font-weight: 500;
  color: #1e293b;
  margin-right: 10px;
}

body.dark .current-ratio-text {
  color: var(--text-primary, #e2e8f0);
}

.current-ratio-desc {
  font-size: 14px;
  color: #64748b;
  flex-grow: 1;
}

body.dark .current-ratio-desc {
  color: var(--text-secondary, #94a3b8);
}

.expand-icon {
  font-size: 16px;
  color: #64748b;
  transition: transform 0.3s ease;
}

.expand-icon.rotate {
  transform: rotate(180deg);
}

body.dark .expand-icon {
  color: var(--text-secondary, #94a3b8);
}

/* 右侧区域 */
.header-right {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* 积分显示 */
.points-display {
  display: flex;
  align-items: center;
  padding: 3px 10px;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
  border-radius: 8px;
  border: 1px solid rgba(99, 102, 241, 0.2);
  color: #333;
  transition: all 0.3s;
  cursor: pointer;
}

.points-display:hover {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.15), rgba(139, 92, 246, 0.15));
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.2);
}

body.dark .points-display:hover {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.25), rgba(139, 92, 246, 0.25));
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
}

.points-display .el-icon {
  color: #6366f1;
  margin-right: 6px;
  font-size: 14px;
}

.points-value {
  font-size: 14px;
  font-weight: 600;
}

body.dark .points-display {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.2), rgba(139, 92, 246, 0.2));
  border: 1px solid rgba(99, 102, 241, 0.3);
  color: var(--text-primary);
}

/* 导出按钮 */
.export-button {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .video-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  :deep(.export-dialog) {
    width: 95% !important;
  }
}

@media (max-width: 480px) {
  .video-grid {
    grid-template-columns: 1fr;
  }
}
</style> 