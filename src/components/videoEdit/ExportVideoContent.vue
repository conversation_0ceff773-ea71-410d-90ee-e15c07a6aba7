<template>
  <div class="export-video-content">
    <!-- 内容区域 -->
    <div class="dialog-content" v-if="videos.length > 0">
      <!-- 历史导出视频 -->
      <div class="export-history">
        <!-- <h3 class="export-section-title">历史视频</h3> -->

        <!-- 加载中状态 -->
        <div v-if="isLoadingVideos" class="export-loading">
          <div class="loading-spinner"></div>
        </div>

        <!-- 空数据状态 -->
        <div v-else-if="videos.length === 0" class="export-empty">
          <el-icon class="empty-icon">
            <VideoCamera />
          </el-icon>
          <div class="empty-text">暂无导出视频</div>
        </div>

        <!-- 视频列表 -->
        <div v-else class="export-videos">
          <div class="video-grid">
            <div v-for="(video, index) in videos" :key="video.id || index" class="video-item">
              <div class="video-card" @click="handleVideoClick(video)">
                <div class="video-preview">
                  <img class="thumbnail" v-if="video.status == 2"
                    :src="`${video.videoUrl}?x-oss-process=video/snapshot,t_0,f_jpg`" alt="视频封面">
                  <div class="duration" v-if="video.status == 2">{{ formatDuration(video.videoDuration) }}</div>
                  <div class="play-icon">
                    <el-icon>
                      <VideoPlay />
                    </el-icon>
                  </div>
                  <!-- 添加下载按钮 -->
                  <div v-if="video.status == 2" class="download-icon" @click.stop="handleDownload(video)">
                    <el-icon>
                      <Download />
                    </el-icon>
                  </div>
                  <!-- 添加分享按钮 -->
                  <div v-if="video.status == 2" class="share-icon" @click.stop="handleShare(video)">
                    <el-icon>
                      <Share v-if="!video.shareStatus || video.shareStatus === 0" />
                      <Link v-else />
                    </el-icon>
                  </div>
                  <!-- 添加视频状态标签 -->
                  <div v-if="video.status !== 2" class="status-badge">
                    <el-icon>
                      <Loading class="loading-icon" />
                    </el-icon>
                    （生成）{{ video.statusDesc }}...
                  </div>
                </div>
                <div class="video-info">
                  <div class="video-meta">
                    <span>生成于：{{ formatDate(video.createTime) }}</span>
                    <!-- <span>{{ video.resolution }}</span> -->
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="dialog-footer" v-if="!isExporting && !hasUnfinishedVideos">
      <!-- 字幕开关 -->
      <div class="export-subtitles">
        <div class="subtitle-switch">
          <el-switch v-model="showSubtitles" inline-prompt active-text="字幕" inactive-text="字幕" size="large"
            class="subtitle-switch-component" style="--el-switch-on-color: #706cefd7" @change="setShowSubtitles" />
        </div>
      </div>

      <!-- <div class="cancel-btn" @click="handleClose" :disabled="isExporting">取消</div> -->
      <div class="export-btn" @click="handleExport" :disabled="isExporting || hasUnfinishedVideos">
        <!-- <el-icon v-if="!isExporting && !hasUnfinishedVideos">
          <Download />
        </el-icon> -->
        <span>{{ isExporting || hasUnfinishedVideos ? '生成中...' : '视频生成' }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, onBeforeUnmount, computed } from 'vue';
import { VideoCamera, VideoPlay, Download, Loading, Share, Link } from '@element-plus/icons-vue';
import { ElMessage, ElSwitch, ElMessageBox } from 'element-plus';
import { getUserVideo, getExportVideoList, exportVideo, videoRenderShareLink } from '@/api/auth.js';

// 轮询相关常量
const POLLING_INTERVAL = 5000; // 轮询间隔，5秒

const props = defineProps({
  defaultResolution: {
    type: String,
    default: '1080p'
  },
  defaultShowSubtitles: {
    type: Boolean,
    default: true
  },
  defaultFps: {
    type: Number,
    default: 24
  },
  isExporting: {
    type: Boolean,
    default: false
  },
  canvasId: {
    type: [Number, String],
    default: ''
  }
});

const emit = defineEmits([
  'video-click',
  'page-change',
  'export',
  'update:resolution',
  'update:showSubtitles',
  'update:fps',
  'update:isExporting',
  'close'
]);

// 分页相关
const currentPage = ref(1);
const pageSize = ref(3);

// 视频数据状态
const videos = ref([]);
const isLoadingVideos = ref(false);
const totalVideos = ref(0);

// 轮询相关变量
const pollingTimer = ref(null); // 轮询定时器

// 分辨率选择
const resolution = ref(props.defaultResolution);

// FPS 选择
const fps = ref(props.defaultFps);

// 字幕开关
const showSubtitles = ref(props.defaultShowSubtitles);

// 计算属性：检查是否有未完成的视频（状态不为2）
const hasUnfinishedVideos = computed(() => {
  return videos.value.some(video => video.status !== 2 && video.status !== 3);
});

// 监听props中的分辨率变化
watch(() => props.defaultResolution, (newVal) => {
  resolution.value = newVal;
});

// 监听props中的FPS变化
watch(() => props.defaultFps, (newVal) => {
  fps.value = newVal;
});

// 监听props中的字幕开关变化
watch(() => props.defaultShowSubtitles, (newVal) => {
  showSubtitles.value = newVal;
});

// 获取历史导出视频
const fetchExportVideos = async () => {
  try {
    // 检查是否有画布ID
    if (!props.canvasId) {
      console.warn('缺少画布ID，无法获取导出视频列表');
      videos.value = [];
      return;
    }

    const response = await getExportVideoList(props.canvasId);

    if (response && response.success) {
      // 只取前两个元素
      videos.value = response.data.slice(0, 1) || [];
      totalVideos.value = videos.value.length;
      console.log('获取导出视频列表成功:', videos.value);

      // 检查视频状态，决定是否需要轮询
      checkVideosStatus();
    } else {
      console.error('获取导出视频列表失败:', response?.errMessage);
      ElMessage.error(response?.errMessage || '获取导出视频列表失败');
      videos.value = [];

      // 出错时停止轮询
      stopPolling();
    }
  } catch (error) {
    console.error('获取视频数据出错:', error);
    ElMessage.error('获取视频数据时发生错误');
    videos.value = [];
  } finally {
    isLoadingVideos.value = false;
  }
};

// 关闭弹框
const handleClose = () => {
  if (props.isExporting) return;

  // 停止轮询
  stopPolling();

  emit('close');
};

// 处理视频点击
const handleVideoClick = (video) => {
  emit('video-click', video);
};

// 设置分辨率
const setResolution = (res) => {
  resolution.value = res;
  emit('update:resolution', res);
};

// 设置帧率
const setFps = (value) => {
  fps.value = value;
  emit('update:fps', value);
};

// 处理导出
const handleExport = async () => {
  // 检查是否有画布ID
  if (!props.canvasId) {
    ElMessage.error('缺少画布ID，无法导出视频');
    return;
  }

  try {
    // 构建参数
    const params = {
      canvasId: props.canvasId,
      resolution: resolution.value,
      showSubtitle: showSubtitles.value ? 1 : 0,
      fps: fps.value
    };

    // 显示导出中状态
    // emit('update:isExporting', true);

    // 调用导出API
    const response = await exportVideo(params);

    if (response && response.success) {
      // 刷新导出视频列表
      setTimeout(() => {
        fetchExportVideos();
      }, 1000);

      // 通知父组件导出成功
      // emit('export', {
      //   resolution: resolution.value,
      //   showSubtitles: showSubtitles.value,
      //   fps: fps.value,
      //   success: true,
      //   data: response.data
      // });
    } else {
      ElMessage.error(response?.errMessage || '视频导出失败');

      // 通知父组件导出失败
      emit('export', {
        resolution: resolution.value,
        showSubtitles: showSubtitles.value,
        fps: fps.value,
        success: false,
        error: response?.errMessage
      });
    }
  } catch (error) {
    console.error('导出视频出错:', error);
    ElMessage.error('导出视频时发生错误，请重试');

    // 通知父组件导出失败
    emit('export', {
      resolution: resolution.value,
      showSubtitles: showSubtitles.value,
      fps: fps.value,
      success: false,
      error: error.message
    });
  } finally {
    // 恢复导出状态
    emit('update:isExporting', false);
  }
};

// 格式化日期，如果是今年则不显示年，显示时分秒
const formatDate = (timestamp) => {
  if (!timestamp) return '';
  const date = new Date(timestamp);
  if (date.getFullYear() === new Date().getFullYear()) {
    return `${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
  } else {
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
  }
};

// 格式化视频时长
const formatDuration = (milliseconds) => {
  if (!milliseconds) return '00:00';
  // 将毫秒转换为秒
  const seconds = milliseconds / 1000;
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${String(mins).padStart(2, '0')}:${String(secs).padStart(2, '0')}`;
};

// 设置字幕开关
const setShowSubtitles = (value) => {
  showSubtitles.value = value;
  emit('update:showSubtitles', value);
};

// 处理视频下载
const handleDownload = async (video) => {
  if (!video || !video.videoUrl) {
    ElMessage.error('视频链接不存在');
    return;
  }

  try {
    ElMessage.info('准备下载视频，请稍候...');

    // 使用fetch API获取视频文件
    const response = await fetch(video.videoUrl);

    // 检查响应状态
    if (!response.ok) {
      throw new Error(`下载失败: ${response.status} ${response.statusText}`);
    }

    // 将响应转换为blob
    const blob = await response.blob();

    // 创建一个blob URL
    const blobUrl = URL.createObjectURL(blob);

    // 创建一个临时的a标签用于下载
    const link = document.createElement('a');
    link.href = blobUrl;
    link.download = `视频_${formatDate(video.createTime)}_${video.resolution}.mp4`;
    link.style.display = 'none';
    document.body.appendChild(link);

    // 触发点击下载
    link.click();

    // 清理
    setTimeout(() => {
      document.body.removeChild(link);
      URL.revokeObjectURL(blobUrl); // 释放blob URL
    }, 100);

    ElMessage.success('视频下载已开始');
  } catch (error) {
    console.error('下载视频出错:', error);
    ElMessage.error(`下载视频失败: ${error.message}`);

    // 如果fetch失败，尝试直接在新标签页中打开视频
    window.open(video.videoUrl, '_blank');
  }
};

// 处理视频分享
const handleShare = async (video) => {
  if (!video || !video.id) {
    ElMessage.error('视频信息不完整');
    return;
  }

  try {
    // 如果已经分享，则复制链接或取消分享
    if (video.shareStatus === 1 && video.shareUrl) {
      // 显示操作选择
      const action = await ElMessageBox.confirm(
        '视频已分享，是否要复制链接或取消分享？',
        '分享操作',
        {
          distinguishCancelAndClose: true,
          confirmButtonText: '复制链接',
          cancelButtonText: '取消分享',
          type: 'info'
        }
      ).catch((action) => {
        if (action === 'cancel') {
          return 'unshare';
        }
        return 'dismiss';
      });

      if (action === 'unshare') {
        // 取消分享
        await toggleVideoShare(video, false);
      } else if (action === 'confirm') {
        // 复制链接
        copyShareLink(video.shareUrl);
      }
    } else {
      // 创建分享
      await toggleVideoShare(video, true);
    }
  } catch (error) {
    console.error('分享操作失败:', error);
    ElMessage.error('分享操作失败，请重试');
  }
};

// 切换视频分享状态
const toggleVideoShare = async (video, share) => {
  try {
    const response = await videoRenderShareLink({
      taskId: video.id,
      share: share
    });

    if (response && response.success) {
      // 更新视频的分享状态
      const videoIndex = videos.value.findIndex(v => v.id === video.id);
      if (videoIndex !== -1) {
        videos.value[videoIndex].shareStatus = share ? 1 : 0;
        if (share && response.data) {
          videos.value[videoIndex].shareUrl = response.data;
          // 自动复制分享链接
          copyShareLink(response.data);
          ElMessage.success('视频分享成功，链接已复制到剪贴板');
        } else {
          videos.value[videoIndex].shareUrl = '';
          ElMessage.success('已取消视频分享');
        }
      }
      fetchExportVideos();

    } else {
      ElMessage.error(response?.errMessage || '分享操作失败');
    }
  } catch (error) {
    console.error('分享操作出错:', error);
    ElMessage.error('分享操作失败，请重试');
  }
};

// 复制分享链接
const copyShareLink = (shareUrl) => {
  if (!shareUrl) {
    ElMessage.error('分享链接不存在');
    return;
  }

  navigator.clipboard.writeText(shareUrl)
    .then(() => {
      ElMessage.success('分享链接已复制到剪贴板');
    })
    .catch(err => {
      console.error('复制失败:', err);
      ElMessage.error('复制链接失败，请手动复制');
    });
};

// 检查视频状态，判断是否需要轮询
const checkVideosStatus = () => {
  // 检查是否有状态不为2的视频
  const hasUnfinishedVideos = videos.value.some(video => video.status !== 2);

  if (hasUnfinishedVideos) {
    // 如果有未完成的视频，开始轮询
    startPolling();
  } else {
    // 如果所有视频都已完成，停止轮询
    stopPolling();
  }
};

// 开始轮询
const startPolling = () => {
  // 如果已经存在定时器，先清除
  stopPolling();

  // 设置新的定时器
  pollingTimer.value = setInterval(() => {
    console.log('轮询获取视频列表...');
    fetchExportVideos();
  }, POLLING_INTERVAL);

  console.log('开始轮询视频状态');
};

// 停止轮询
const stopPolling = () => {
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value);
    pollingTimer.value = null;
    console.log('停止轮询视频状态');
  }
};

// 组件挂载时获取视频列表
onMounted(() => {
  fetchExportVideos();
});

// 组件卸载时清除定时器
onBeforeUnmount(() => {
  // 确保清除轮询定时器
  stopPolling();
});
</script>

<style scoped>
/* 导出视频内容容器 */
.export-video-content {
  padding: 16px;
  box-sizing: border-box;
  width: 100%;
  max-height: 600px;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  gap: 16px;
}

:deep(.el-switch--large) {
  height: auto;
}

body.dark .export-video-content {
  background-color: var(--bg-card, #1e1e2d);
  color: var(--text-primary, #e2e8f0);
}

/* 内容区域 */
.dialog-content {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 导出历史区域 */
.export-section-title {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  text-align: left;
  margin: 2px 0 10px 0;
}

body.dark .export-section-title {
  color: var(--text-primary);
}

/* 加载中状态 */
.export-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10px;
  border-radius: 8px;
  height: 180px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(99, 102, 241, 0.2);
  border-top-color: #6366f1;
  border-radius: 50%;
  animation: spin 1s infinite linear;
  margin-bottom: 10px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 空状态 */
.export-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10px;
  border-radius: 8px;
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  color: #6366f1;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
  color: #64748b;
}

body.dark .empty-text {
  color: var(--text-secondary);
}

/* 视频网格 */
.video-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 8px;
}

.video-item {
  overflow: hidden;
  border-radius: 12px;
}

.video-card {
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s;
  cursor: pointer;
  height: 100%;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
}

body.dark .video-card {
  background-color: var(--bg-secondary);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
}

.video-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

body.dark .video-card:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.video-preview {
  position: relative;
  aspect-ratio: 16/9;
  overflow: hidden;
  background-color: #000000;
}

.thumbnail {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  transition: transform 0.3s;
  object-fit: contain;
}

.video-card:hover .thumbnail {
  transform: scale(1.05);
}

.duration {
  position: absolute;
  top: 8px;
  left: 8px;
  background-color: rgba(0, 0, 0, 0.7);
  color: #fff;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.play-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 40px;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 20px;
  opacity: 0;
  transition: opacity 0.3s, background-color 0.3s;
}

/* 下载按钮样式 */
.download-icon {
  position: absolute;
  bottom: 8px;
  right: 8px;
  width: 32px;
  height: 32px;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 16px;
  transition: opacity 0.3s, background-color 0.3s;
  cursor: pointer;
  z-index: 3;
}

.download-icon:hover {
  background-color: rgba(99, 102, 241, 0.9);
}

body.dark .download-icon:hover {
  background-color: rgba(79, 70, 229, 0.9);
}

/* 分享按钮样式 */
.share-icon {
  position: absolute;
  bottom: 8px;
  right: 48px;
  width: 32px;
  height: 32px;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 16px;
  transition: opacity 0.3s, background-color 0.3s;
  cursor: pointer;
  z-index: 3;
}

.share-icon:hover {
  background-color: rgba(34, 197, 94, 0.9);
}

body.dark .share-icon:hover {
  background-color: rgba(22, 163, 74, 0.9);
}

.video-card:hover .download-icon,
.video-card:hover .share-icon {
  opacity: 1;
}

.video-card:hover .play-icon {
  opacity: 1;
  background-color: rgba(0, 0, 0, 0.7);
}

/* 视频状态标签 */
.status-badge {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #000000;
  color: rgba(255, 255, 255, 0.82);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 600;
  z-index: 2;
  text-align: center;
}

.status-badge .el-icon {
  animation: spin 1s infinite linear;
  margin-right: 4px;
  font-size: 26px;
}

.video-info {
  padding: 8px;
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #eaebec;
}

body.dark .video-info {
  background-color: var(--bg-card);
}

.video-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #2d2d2d;
}

body.dark .video-meta {
  color: var(--text-secondary);
}

/* 字幕开关 */
.export-subtitles {
  display: flex;
  align-items: center;
  justify-content: center;
}

.subtitle-switch {
  display: flex;
  align-items: center;
  /* padding: 0 10px; */
}

.subtitle-switch-component {
  margin-right: 5px;
}

/* 自定义字幕开关颜色 */
:deep(.subtitle-switch-component.el-switch.is-checked .el-switch__core) {
  background-color: #706cefd7 !important;
  border-color: #706cefd7 !important;
}

:deep(.subtitle-switch-component.el-switch .el-switch__core:hover) {
  border-color: #706cefd7 !important;
}

body.dark .subtitle-switch {
  color: var(--text-secondary);
}

/* 底部按钮区域 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  /* border-top: 1px solid #e5e7eb; */
}

body.dark .dialog-footer {
  border-top-color: var(--border-color);
}

.cancel-btn,
.export-btn {
  flex: 1;
  padding: 4px 12px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  text-align: center;
  justify-content: center;
}

.cancel-btn {
  background-color: #f1f5f9;
  border: 1px solid #e2e8f0;
  color: #64748b;
}

.cancel-btn:hover:not(:disabled) {
  background-color: #e2e8f0;
}

.cancel-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

body.dark .cancel-btn {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
  color: var(--text-secondary);
}

body.dark .cancel-btn:hover:not(:disabled) {
  background-color: var(--bg-hover);
}

.export-btn {
  background-color: #6366f1;
  border: 1px solid #6366f1;
  color: white;
}

.export-btn:hover:not(:disabled) {
  background-color: #4f46e5;
}

.export-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

body.dark .export-btn {
  background-color: #4f46e5;
  border-color: #4f46e5;
}

body.dark .export-btn:hover:not(:disabled) {
  background-color: #4338ca;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .video-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .video-grid {
    grid-template-columns: 1fr;
  }
}
</style>
