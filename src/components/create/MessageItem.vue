<template>
  <!-- 用户消息或AI消息 -->
  <div class="message-item" :data-id="message.id">
    <!-- 用户消息 -->
    <template v-if="message.role === 'user'">
      <AXBubble placement="end" :content="message.content" class="message-bubble" />
    </template>

    <!-- AI消息 -->
    <template v-else>
      <AXBubble 
        placement="start" 
        variant="outlined" 
        :content="message.content" 
        :messageRender="renderMarkdown"
        :loading="isStreaming && message.id === streamingMessageId && (!message.content || message.content.trim() === '')"
        :class="[
          'message-bubble', 
          { 'streaming-message': isStreaming && message.id === streamingMessageId && message.content && message.content.trim() !== '' }
        ]"
      >
        <!-- 添加使用量信息展示 -->
        <template #footer>
          <div class="usage-info" v-if="message.meta && message.meta.usage">
            <div class="usage-item">
              <span>输入:{{ message.meta.usage.prompt_tokens }} tokens</span>
              <span>输出:{{ message.meta.usage.completion_tokens }} tokens</span>
              <!-- <span>价格:{{ message.meta.usage.total_price }}{{ message.meta.usage.currency }}</span> -->
              <!-- <span>积分:-{{ (message.meta.usage.prompt_tokens + message.meta.usage.completion_tokens)/10000 }}</span> -->
              <span>积分:-{{ message.meta.usage.pointsToDeduct }}</span>
              <!-- <span>耗时:{{ (message.meta.usage.latency).toFixed(2) }}s</span> -->
            </div>
          </div>
        </template>
      </AXBubble>

      <!-- 消息元数据（如进度等） -->
      <div v-if="message.meta && message.meta.prompt" class="meta-box prompt-meta meta-start">
        <div class="meta-title">创作提示词：</div>
        <div class="meta-content">{{ message.meta.prompt }}</div>
      </div>
      
      <!-- 建议问题列表 -->
      <div v-if="message.suggestedQuestions && message.suggestedQuestions.length > 0" class="suggested-questions">
        <div class="suggested-questions-title">建议问题：</div>
        <div class="suggested-questions-list">
          <div v-for="(question, qIndex) in message.suggestedQuestions" 
               :key="qIndex" 
               class="suggested-question-item"
               @click="$emit('send-message', question)">
            {{ question }}
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup>
import { ref, nextTick, h } from 'vue'
import { Bubble as AXBubble } from 'ant-design-x-vue'
import { Typography } from 'ant-design-vue'
import markdownit from 'markdown-it'

const props = defineProps({
  message: {
    type: Object,
    required: true
  },
  isStreaming: {
    type: Boolean,
    default: false
  },
  streamingMessageId: {
    type: String,
    default: null
  },
  showPreElements: {
    type: Boolean,
    default: true
  },
  showBlockquoteElements: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['send-message'])

// 初始化 markdown-it
const md = markdownit({
  html: true,
  breaks: true,
  linkify: true
})

// 增加修改图片渲染的逻辑 - 使用全局预览函数
md.renderer.rules.image = function(tokens, idx, options, env, self) {
  const token = tokens[idx]
  const srcIndex = token.attrIndex('src')
  const src = token.attrs[srcIndex][1]
  const alt = token.content || ''
  
  // 使用全局的openImagePreview函数
  return `<img src="${src}" alt="${alt}" title="${alt}" 
    onclick="window.openImagePreview('${src}', '${alt.replace(/'/g, "\\'")}')" 
    style="cursor: zoom-in;" />`
}

// 增加修改链接渲染的逻辑 - 使链接在新窗口打开
md.renderer.rules.link_open = function(tokens, idx, options, env, self) {
  const token = tokens[idx]
  
  // 添加 target="_blank" 和 rel="noopener noreferrer" 属性
  const aIndex = token.attrIndex('href')
  const href = token.attrs[aIndex][1]
  
  // 设置链接在新窗口打开
  // token.attrPush(['target', '_blank'])
  // 添加安全属性，防止新页面对原页面进行操作（安全最佳实践）
  token.attrPush(['rel', 'noopener noreferrer'])
  
  return self.renderToken(tokens, idx, options)
}

/**
 * 将 Markdown 字符串渲染为 HTML 并展示在页面上
 *
 * @param content 要渲染的 Markdown 字符串
 * @returns 返回渲染后的 HTML 结构
 */
const renderMarkdown = (content) => {
  return h(Typography, null, {
    default: () => h('div', {
      innerHTML: md.render(content),
      class: 'markdown-content',
      ref: (el) => {
        if (el) {
          // 在DOM更新后应用折叠逻辑
          nextTick(() => {
            // 处理pre标签的折叠
            const preElements = el.querySelectorAll('pre:not(.processed-element)');
            preElements.forEach((pre, index) => {
              // 添加标记表示此元素已处理，避免重复处理
              pre.classList.add('processed-element');
              
              // 创建折叠元素容器
              const foldableContainer = document.createElement('div');
              foldableContainer.className = 'foldable-container';
              
              // 创建折叠标题
              const foldableHeader = document.createElement('div');
              foldableHeader.className = 'foldable-header';
              foldableHeader.innerHTML = `<span class="fold-indicator"></span> ·执行·`;
              
              // 创建内容容器
              const foldableContent = document.createElement('div');
              foldableContent.className = 'foldable-content';
              
              // 移动pre标签内容到折叠容器中
              pre.parentNode.insertBefore(foldableContainer, pre);
              foldableContent.appendChild(pre);
              foldableContainer.appendChild(foldableHeader);
              foldableContainer.appendChild(foldableContent);
              
              // 默认折叠状态
              foldableContent.style.display = props.showPreElements ? 'block' : 'none';
              foldableHeader.querySelector('.fold-indicator').textContent = props.showPreElements ? '' : ''; // '▼' : '▶'
              
              // 添加点击事件
              foldableHeader.addEventListener('click', () => {
                const isVisible = foldableContent.style.display !== 'none';
                foldableContent.style.display = isVisible ? 'none' : 'block';
                foldableHeader.querySelector('.fold-indicator').textContent = isVisible ? '' : ''; // '▶' : '▼'
              });
            });
            
            // 处理blockquote标签的折叠
            const blockquoteElements = el.querySelectorAll('blockquote:not(.processed-element)');
            blockquoteElements.forEach(blockquote => {
              // 添加标记表示此元素已处理，避免重复处理
              blockquote.classList.add('processed-element');
              
              // 创建折叠元素容器
              const foldableContainer = document.createElement('div');
              foldableContainer.className = 'foldable-container';
              
              // 创建折叠标题
              const foldableHeader = document.createElement('div');
              foldableHeader.className = 'foldable-header';
              foldableHeader.innerHTML = '<span class="fold-indicator"></span> ·思考·';
              
              // 创建内容容器
              const foldableContent = document.createElement('div');
              foldableContent.className = 'foldable-content';
              
              // 移动blockquote标签内容到折叠容器中
              blockquote.parentNode.insertBefore(foldableContainer, blockquote);
              foldableContent.appendChild(blockquote);
              foldableContainer.appendChild(foldableHeader);
              foldableContainer.appendChild(foldableContent);
              
              // 默认折叠状态
              foldableContent.style.display = props.showBlockquoteElements ? 'block' : 'none';
              // if(foldableHeader.querySelector('.fold-indicator')){
              //   foldableHeader.querySelector('.fold-indicator').textContent = props.showBlockquoteElements ? '▼' : '▶';
              // }
              
              // 添加点击事件
              foldableHeader.addEventListener('click', () => {
                const isVisible = foldableContent.style.display !== 'none';
                foldableContent.style.display = isVisible ? 'none' : 'block';
                // if(foldableHeader.querySelector('.fold-indicator')){
                //   foldableHeader.querySelector('.fold-indicator').textContent = isVisible ? '▼' : '▶';
                // }
              });
            });
          });
        }
      }
    })
  });
}
</script>

<style scoped>
.message-item {
  margin-bottom: 16px;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message-bubble {
  max-width: 85%;
}

/* 元数据样式 */
.meta-box {
  background-color: #f9fafc;
  border-radius: 8px;
  padding: 12px;
  margin-top: 8px;
  font-size: 14px;
  max-width: 85%;
  transition: background-color 0.3s;
}

body.dark .meta-box {
  background-color: var(--bg-tertiary);
}

.meta-title {
  font-weight: 500;
  margin-bottom: 6px;
  color: #606266;
  transition: color 0.3s;
}

body.dark .meta-title {
  color: var(--text-tertiary);
}

.meta-content {
  white-space: pre-line;
  color: #303133;
  text-align: left;
  transition: color 0.3s;
}

body.dark .meta-content {
  color: var(--text-secondary);
}

.prompt-meta {
  border-left: 2px solid #6366f1;
}

/* 元数据位置 */
.meta-start {
  margin-left: 48px;
  align-self: flex-start;
}

/* 正在输入中的消息样式 */
.streaming-message {
  position: relative;
}

.streaming-message::after {
  content: '';
  display: inline-block;
  width: 6px;
  height: 16px;
  background-color: #40a0ffc7;
  animation: cursor-blink 1s step-end infinite;
  margin-left: 4px;
  vertical-align: middle;
  position: absolute;
  border-radius: 6px;
  right: -10px;
  bottom: 0px;
}

@keyframes cursor-blink {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}

/* 建议问题样式 */
.suggested-questions {
  margin-top: 20px;
  margin-left: 4px;
  max-width: 100%;
  text-align: center;
}

.suggested-questions-title {
  font-size: 12px;
  color: #909399;
  margin-bottom: 8px;
  text-align: center;
  width: 100%;
  transition: color 0.3s;
}

body.dark .suggested-questions-title {
  color: var(--text-tertiary);
}

.suggested-questions-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  text-align: right;
  width: 100%;
  justify-content: center;
}

.suggested-question-item {
  padding: 6px 12px;
  background-color: #f4f4f5;
  border-radius: 4px;
  font-size: 13px;
  color: #606266;
  cursor: pointer;
  transition: all 0.3s ease;
}

body.dark .suggested-question-item {
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
}

.suggested-question-item:hover {
  background-color: #e9e9eb;
  color: #409eff;
}

body.dark .suggested-question-item:hover {
  background-color: var(--bg-hover);
  color: var(--primary-color);
}

/* 使用量信息样式 */
.usage-info {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-top: 6px;
  font-size: 12px;
  color: #909399;
  transition: color 0.3s;
}

body.dark .usage-info {
  color: var(--text-tertiary);
}

.usage-item {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  justify-content: flex-end;
}

.usage-item span {
  white-space: nowrap;
}

/* 添加响应式样式 */
@media (max-width: 640px) {
  .usage-item {
    flex-direction: column;
    gap: 4px;
  }
}

/* 添加折叠容器相关样式 */
:deep(.foldable-container) {
  margin: 0px 0;
  border-radius: 10px;
  overflow: hidden;
  transition: border-color 0.3s;
}

/* body.dark :deep(.foldable-container) {
  border-color: var(--border-color);
} */

:deep(.foldable-header) {
  padding: 2px 2px;
  font-size: 13px;
  color: #8888888f;
  cursor: pointer;
  user-select: none;
  display: flex;
  align-items: center;
  transition: background-color 0.3s, color 0.3s;
}

/* body.dark :deep(.foldable-header) {
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
} */

:deep(.fold-indicator) {
  display: inline-block;
  margin-right: 2px;
  font-size: 12px;
  transition: transform 0.2s;
  width: 12px;
  text-align: center;
}

:deep(.foldable-content) {
  transition: height 0.3s;
}

:deep(.foldable-content pre),
:deep(.foldable-content blockquote) {
  margin: 0;
  border-radius: 0px;
  border: none;
  box-shadow: none;
}

/* 修改pre和blockquote在折叠容器中的样式 */
:deep(.foldable-content pre) {
  max-height: none;
  border-radius: 10px;
}

:deep(.foldable-content blockquote) {
  margin: 0;
  border-radius: 0;
}

/* Markdown 内容样式 */
:deep(.markdown-content) {
  margin: 0;
  font-size: 4px;
}

:deep(.markdown-content p) {
  margin: 0;
  margin-bottom: 1px;
  display: inline-block;
  width: 100%;
  font-size: 14px;
  transition: color 0.3s;
  background-color: #f0f0f055;
  border-radius: 8px;
  padding: 10px;
  box-sizing: border-box;
}

:deep(.markdown-content ol) {
  padding: 10px 0;
}

body.dark .markdown-content p {
  color: #000000a9;
}

/* body.dark :deep(.markdown-content p) {
  color: var(--text-primary);
  background-color: var(--bg-tertiary);
} */

/* 设置更紧凑的标题间距 */
:deep(.markdown-content h1),
:deep(.markdown-content h2),
:deep(.markdown-content h3),
:deep(.markdown-content h4),
:deep(.markdown-content h5),
:deep(.markdown-content h6) {
  margin-top: 6px;
  margin-bottom: 2px;
  font-weight: 600;
}

:deep(.markdown-content h1) {
  font-size: 22px;
}

:deep(.markdown-content h2) {
  font-size: 20px;
}

:deep(.markdown-content h3) {
  font-size: 18px;
}

:deep(.markdown-content h4) {
  font-size: 16px;
}

:deep(.markdown-content h5),
:deep(.markdown-content h6) {
  font-size: 14px;
}

/* 第一个标题不需要顶部边距 */
:deep(.markdown-content h1:first-child),
:deep(.markdown-content h2:first-child),
:deep(.markdown-content h3:first-child),
:deep(.markdown-content h4:first-child) {
  margin-top: 0;
}

/* 标题后跟列表时减小间距 */
:deep(.markdown-content h1 + ul),
:deep(.markdown-content h1 + ol),
:deep(.markdown-content h2 + ul),
:deep(.markdown-content h2 + ol),
:deep(.markdown-content h3 + ul),
:deep(.markdown-content h3 + ol),
:deep(.markdown-content h4 + ul),
:deep(.markdown-content h4 + ol),
:deep(.markdown-content h5 + ul),
:deep(.markdown-content h5 + ol),
:deep(.markdown-content h6 + ul),
:deep(.markdown-content h6 + ol) {
  margin-top: 0;
  margin-bottom: 0;
}

:deep(.markdown-content br) {
  display: none;
}

:deep(.markdown-content p:empty) {
  display: none;
}

:deep(.markdown-content blockquote) {
  margin: 4px 0;
  padding: 2px 10px;
  border-left: 3px solid #7274f2a7;
  color: #828282;
  transition: all 0.3s;
}

/* body.dark :deep(.markdown-content blockquote) {
  border-left-color: var(--border-color);
  color: var(--text-secondary);
} */

:deep(.markdown-content a) {
  color: #1890ff;
  text-decoration: none;
  transition: color 0.3s;
}

/* body.dark :deep(.markdown-content a) {
  color: var(--primary-color);
} */

:deep(.markdown-content a:hover) {
  text-decoration: underline;
}

:deep(.markdown-content pre) {
  font-family: 'Courier New', Courier, monospace;
  font-size: 13px;
  overflow: auto;
  transition: background-color 0.3s;
  margin: 8px 0;
  border: 1px solid #eaecef22;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.128);
  max-height: 300px;
  border-radius: 10px;
}

:deep(.markdown-content code) {
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', Courier, monospace;
  font-size: 12px;
  transition: background-color 0.3s;
  color: #476582;
}

/* body.dark :deep(.markdown-content code) {
  color: #a2a9b6;
} */

/* 在pre内的code不需要背景色和内边距 */
:deep(.markdown-content pre code) {
  background-color: transparent;
  padding: 0;
  font-size: 13px;
  color: inherit;
}

/* body.dark :deep(.markdown-content pre code) {
  color: #e0e0e0;
} */

:deep(.markdown-content ul),
:deep(.markdown-content ol) { 
  margin-left: 20px;
  font-size: 0px; /* li 的间距 */
}

:deep(.markdown-content li) {
  font-size: 14px;
  padding: 0;
  margin: 0;
}

:deep(.markdown-content li + li) {
  margin-top: 0;
}

/* 修改图片样式为预览模式 */
:deep(.markdown-content img) {
  max-width: 100%;
  width: auto;
  max-height: 80px;
  border-radius: 8px;
  cursor: zoom-in;
  margin: 10px 0;
  transition: transform 0.2s ease, filter 0.3s;
}

body.dark :deep(.markdown-content img) {
  filter: brightness(0.9);
}

:deep(.markdown-content img:hover) {
  transform: scale(1.02);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

/* body.dark :deep(.markdown-content img:hover) {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.2);
} */

/* 所有标签间的行间距调整，最小化间距 */
:deep(.markdown-content > *) {
  margin-top: 0;
  margin-bottom: 1px;
}

:deep(.markdown-content hr) {
  margin: 3px 0;
  border: 0;
  border-top: 1px solid #eaecef;
}

/* 直接相邻的标题间没有间距 */
:deep(.markdown-content h1 + h1),
:deep(.markdown-content h1 + h2),
:deep(.markdown-content h1 + h3),
:deep(.markdown-content h1 + h4),
:deep(.markdown-content h1 + h5),
:deep(.markdown-content h1 + h6) {
  margin-top: 3px;
}

/* 段落之间的间距缩小 */
:deep(.markdown-content p + p) {
  margin-top: 1px;
}

/* 针对表格样式 */
:deep(.markdown-content table) {
  border-collapse: collapse;
  font-size: 0.9em;
}

:deep(.markdown-content table th),
:deep(.markdown-content table td) {
  padding: 4px 8px;
  border: 1px solid #dfe2e5;
  transition: border-color 0.3s, background-color 0.3s, color 0.3s;
}
:deep(.markdown-content table th){
  font-size: 12px;
}
:deep(.markdown-content table tr) {
  font-size: 10px;
  background-color: var(--border-color);
}

/* body.dark :deep(.markdown-content table th),
body.dark :deep(.markdown-content table td) {
  color: var(--text-secondary);
} */

:deep(.markdown-content table th) {
  font-weight: 600;
}

/* body.dark :deep(.markdown-content table th) {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
} */
</style> 