<template>
  <div class="scene-content">
    <!-- CSS Grid 布局 -->
    <div class="shots-grid">
      <!-- 添加骨架屏加载效果，在数据加载完成前显示 -->
      <div v-if="!isDataReady" class="skeleton-container">
        <div v-for="i in 6" :key="i" class="skeleton-card">
          <div class="skeleton-header"></div>
          <div class="skeleton-content"></div>
        </div>
      </div>

      <!-- 真实内容，仅在数据加载完成后显示 -->
      <div v-else class="grid-container" :style="{ gridTemplateColumns: `repeat(auto-fill, minmax(${idealCardWidth}px, 1fr))` }">
        <!-- 分镜循环 -->
        <div v-for="shot in props.shots" :key="shot.id" class="shot-card">
          <!-- 新的分镜卡片结构：左侧图片，右侧内容 -->
          <div class="shot-main">
            <!-- 左侧：图片 +'?x-oss-process=image/resize,w_'+idealCardWidth -->
            <div class="shot-image-container" @click.stop="openShotPreview(shot)" :style="{ aspectRatio: props.imageSize.split(':')[0]/props.imageSize.split(':')[1] }">
              <ImagePreview type="shot" :imageUrl="shot.image" :title="`${shot.sceneName} - ${shot.type}`"
                :itemId="shot.id" :state="shot.imageStatus" :conversationId="conversationId" :sceneId="shot.id"
                @error="handleImageError" :isClick="false" />
              <!-- 分镜ID标签 - 左上角 -->
              <div class="shot-id-tag" @click.stop="insertTextAtCursor('分镜id:' + shot.id)">
                {{ shot.id }}
              </div>
              
              <!-- 警告图标 - 右上角 -->
              <div class="shot-warning-tag" v-if="isShotIncomplete(shot)" @click.stop>
                <el-tooltip :content="getShotWarningMessage(shot)" placement="top" :effect="'light'">
                  <el-icon>
                    <Warning />
                  </el-icon>
                </el-tooltip>
              </div>
            </div>

            <!-- 右侧：旁白和对话 -->
            <div class="shot-content-container" @click="showShotDetails(shot.id)">

              <!-- 添加持续时间标签 - 右上角 -->
              <div class="shot-duration-tag" v-if="shot.duration && shot.voice">
                {{ formatDuration(shot.duration) }}
              </div>

              <!-- 旁白 -->
              <div class="shot-dialogue pangbai simplified">
                <div class="dialogue-text" v-if="!editingState.shots[shot.id] && shot.narration">
                  <!-- <span class="dialogue-content" v-if="shot.narration">{{ shot.narration }}</span> -->
                  <div class="dialogue-content" v-if="shot.lineList && shot.lineList.length > 0">
                    <!-- <div v-for="lineObj in shot.lineList" :key="lineObj.id" class="dialogue-item">
                      <span class="dialogue-character">{{ lineObj.name }}</span>
                      <span class="dialogue-content">{{ lineObj.line }}</span>
                    </div> -->
                    <!-- class="dialogue-content" -->
                    <span v-for="lineObj in shot.lineList" :key="lineObj.id">
                      <span v-if="lineObj.voiceType == 2" class="voice-yinxiao-tag">音效~</span>
                      <span v-if="lineObj.voiceType != 2" class="voice-yinxiao-tag">{{ lineObj.name }}</span>
                      <span v-if="lineObj.voiceType != 2">{{ lineObj.line }}</span>
                    </span>
                  </div>
                </div>

                <el-input v-if="editingState.shots[shot.id]" v-model="tempShots.find(s => s.id === shot.id).narration" type="textarea"
                  :autosize="{ minRows: 2, maxRows: 8 }" placeholder="请输入旁白内容" class="edit-textarea"
                  @click.stop></el-input>

                <!-- 操作按钮 -->
                <div class="shot-actions" v-if="!editingState.shots[shot.id] && shot.narration">
                  <!-- 编辑按钮 -->
                  <el-tooltip content="编辑" placement="top" :effect="'light'">
                    <!-- startEditingShot(shot.id) -->
                    <div class="edit-btn small" @click.stop="showShotDetails(shot.id)">
                      <el-icon>
                        <Edit />
                      </el-icon>
                    </div>
                  </el-tooltip>
                </div>

                <!-- 编辑状态下的操作按钮 -->
                <div class="shot-actions" v-else-if="editingState.shots[shot.id]">
                  <el-tooltip content="取消" placement="top" :effect="'light'">
                    <div class="cancel-btn small" @click.stop="cancelEditingShot(shot.id)">
                      <el-icon>
                        <Close />
                      </el-icon>
                    </div>
                  </el-tooltip>
                  <el-tooltip content="保存" placement="top" :effect="'light'">
                    <div class="save-btn small" @click.stop="saveEditingShot(shot.id, shot.sceneId)">
                      <el-icon>
                        <CircleCheck />
                      </el-icon>
                    </div>
                  </el-tooltip>
                </div>

                <!-- 生成旁白按钮 -->
                <el-tooltip content="生成分镜旁白" placement="top" :effect="'light'" v-else>
                  <div class="scene-group-icon-btn"
                    @click.stop="startEditingShot(shot.id)">
                    生成旁白
                  </div>
                </el-tooltip>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 分镜详情对话框 -->
    <el-dialog v-model="showingDetails" :title="dialogTitle" :show-close="true" width="1100px" @close="hideShotDetails"
      destroy-on-close>
      <div v-if="currentShotData">
        <div class="detail-dialog-content">
          <!-- 添加场景名称信息 -->
          <div class="scene-info-bar">
            <div class="scene-info-label">所属场景:</div>
            <div class="scene-info-value">{{ sceneName }}</div>
          </div>

          <!-- 左右两栏布局 -->
          <div class="detail-layout">
            <!-- 左侧：图片 -->
            <div class="detail-image-container">
              <img :src="currentShotData.image" alt="分镜图片" class="detail-image">
            </div>

            <!-- 右侧：详情内容 -->
            <div class="detail-content">
              <div class="detail-card">
                <div class="detail-item" v-if="false">
                  <div class="detail-label">
                    <el-icon>
                      <Picture />
                    </el-icon>
                    <span>构图</span>

                    <div class="field-actions">
                      <template v-if="!editingState.details[`${currentShotId}_composition`]">
                        <el-tooltip content="编辑" placement="top" :effect="'light'">
                          <div class="edit-btn" @click.stop="startEditingDetail(currentShotId, 'composition')">
                            <el-icon>
                              <Edit />
                            </el-icon>
                          </div>
                        </el-tooltip>
                      </template>
                      <template v-else>
                        <el-tooltip content="取消" placement="top" :effect="'light'">
                          <div class="cancel-btn" @click.stop="cancelEditingDetail(currentShotId, 'composition')">
                            <el-icon>
                              <Close />
                            </el-icon>
                          </div>
                        </el-tooltip>
                        <el-tooltip content="保存" placement="top" :effect="'light'">
                          <div class="save-btn"
                            @click.stop="saveEditingDetail(currentShotId, 'composition', currentShotData.sceneId)">
                            <el-icon>
                              <CircleCheck />
                            </el-icon>
                          </div>
                        </el-tooltip>
                      </template>
                    </div>
                  </div>
                  <div class="detail-value" v-if="!editingState.details[`${currentShotId}_composition`]">
                    {{ currentShotData?.composition || '未设置' }}
                  </div>
                  <el-input v-else v-model="tempCurrentShotData.composition" type="textarea" :rows="2"
                    placeholder="请输入构图描述" class="edit-textarea"></el-input>
                </div>

                <div class="detail-item" v-if="false">
                  <div class="detail-label">
                    <el-icon>
                      <VideoPlay />
                    </el-icon>
                    <span>镜头类型</span>

                    <div class="field-actions">
                      <template v-if="!editingState.details[`${currentShotId}_type`]">
                        <el-tooltip content="编辑" placement="top" :effect="'light'">
                          <div class="edit-btn" @click.stop="startEditingDetail(currentShotId, 'type')">
                            <el-icon>
                              <Edit />
                            </el-icon>
                          </div>
                        </el-tooltip>
                      </template>
                      <template v-else>
                        <el-tooltip content="取消" placement="top" :effect="'light'">
                          <div class="cancel-btn" @click.stop="cancelEditingDetail(currentShotId, 'type')">
                            <el-icon>
                              <Close />
                            </el-icon>
                          </div>
                        </el-tooltip>
                        <el-tooltip content="保存" placement="top" :effect="'light'">
                          <div class="save-btn"
                            @click.stop="saveEditingDetail(currentShotId, 'type', currentShotData.sceneId)">
                            <el-icon>
                              <CircleCheck />
                            </el-icon>
                          </div>
                        </el-tooltip>
                      </template>
                    </div>
                  </div>
                  <div class="detail-value shot-type-value" v-if="!editingState.details[`${currentShotId}_type`]">
                    {{ currentShotData?.type || '未设置' }}
                  </div>
                  <el-input v-else v-model="tempCurrentShotData.type" placeholder="请输入镜头类型"
                    class="edit-input"></el-input>
                </div>

                <div class="detail-item">
                  <div class="detail-label">
                    <el-icon>
                      <ArrowRight />
                    </el-icon>
                    <span>运动</span>

                    <div class="field-actions" v-if="false">
                      <template v-if="!editingState.details[`${currentShotId}_movement`]">
                        <el-tooltip content="编辑" placement="top" :effect="'light'">
                          <div class="edit-btn" @click.stop="startEditingDetail(currentShotId, 'movement')">
                            <el-icon>
                              <Edit />
                            </el-icon>
                          </div>
                        </el-tooltip>
                      </template>
                      <template v-else>
                        <el-tooltip content="取消" placement="top" :effect="'light'">
                          <div class="cancel-btn" @click.stop="cancelEditingDetail(currentShotId, 'movement')">
                            <el-icon>
                              <Close />
                            </el-icon>
                          </div>
                        </el-tooltip>
                        <el-tooltip content="保存" placement="top" :effect="'light'">
                          <div class="save-btn"
                            @click.stop="saveEditingDetail(currentShotId, 'movement', currentShotData.sceneId)">
                            <el-icon>
                              <CircleCheck />
                            </el-icon>
                          </div>
                        </el-tooltip>
                      </template>
                    </div>
                  </div>
                  <div class="detail-value shot-movement-value"
                    v-if="!editingState.details[`${currentShotId}_movement`]">
                    {{ currentShotData?.movement || '未设置' }}
                  </div>
                  <el-input v-else v-model="tempCurrentShotData.movement" placeholder="请输入运动描述"
                    class="edit-input"></el-input>
                </div>

                <div class="detail-item">
                  <div class="detail-label character-label">
                    <el-icon>
                      <User />
                    </el-icon>
                    <span>角色</span>

                    <div class="field-actions" v-if="false">
                      <template v-if="!editingState.details[`${currentShotId}_characters`]">
                        <el-tooltip content="编辑" placement="top" :effect="'light'">
                          <div class="edit-btn" @click.stop="startEditingDetail(currentShotId, 'characters')">
                            <el-icon>
                              <Edit />
                            </el-icon>
                          </div>
                        </el-tooltip>
                      </template>
                      <template v-else>
                        <el-tooltip content="取消" placement="top" :effect="'light'">
                          <div class="cancel-btn" @click.stop="cancelEditingDetail(currentShotId, 'characters')">
                            <el-icon>
                              <Close />
                            </el-icon>
                          </div>
                        </el-tooltip>
                        <el-tooltip content="保存" placement="top" :effect="'light'">
                          <div class="save-btn"
                            @click.stop="saveEditingDetail(currentShotId, 'characters', currentShotData.sceneId)">
                            <el-icon>
                              <CircleCheck />
                            </el-icon>
                          </div>
                        </el-tooltip>
                      </template>
                    </div>
                  </div>
                  <div class="character-list">
                    <div v-if="!editingState.details[`${currentShotId}_characters`]">
                      <div v-if="currentShotData?.characters && currentShotData.characters.length > 0"
                        class="character-list-container">
                        <div v-for="(character, i) in currentShotData.characters" :key="i" class="multi-value-item">
                          <span class="multi-value-tag character-tag">{{ character }}</span>
                        </div>
                      </div>
                      <div v-else class="empty-characters">
                        暂无角色信息
                      </div>
                    </div>
                    <div v-else>
                      <div class="character-list-container editing">
                        <div v-for="(character, i) in tempCurrentShotData.characters || []" :key="i"
                          class="multi-value-item editing">
                          <el-input v-model="tempCurrentShotData.characters[i]" placeholder="请输入角色名称"
                            class="edit-input character-input"></el-input>
                          <el-button type="danger" size="small" circle @click="removeCharacter(currentShotId, i)"
                            class="remove-character-btn">
                            <el-icon>
                              <Close />
                            </el-icon>
                          </el-button>
                        </div>
                        <el-button type="primary" size="small" @click="addCharacter(currentShotId)"
                          class="add-character-btn">
                          <el-icon>
                            <Plus />
                          </el-icon> 添加角色
                        </el-button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 添加对话内容区域 -->
              <div class="detail-card dialogue-card" v-if="currentShotData?.lineList">
                <div class="detail-label dialogue-label">
                  <el-icon>
                    <ChatDotRound />
                  </el-icon>
                  <span>对话内容</span>

                  <div class="field-actions">
                    <template v-if="!editingState.details[`${currentShotId}_lines`]">
                      <el-tooltip content="编辑" placement="top" :effect="'light'">
                        <div class="edit-btn" @click.stop="startEditingDetail(currentShotId, 'lines')">
                          <el-icon>
                            <Edit />
                          </el-icon>
                        </div>
                      </el-tooltip>
                    </template>
                    <template v-else>
                      <el-tooltip content="取消" placement="top" :effect="'light'">
                        <div class="cancel-btn" @click.stop="cancelEditingDetail(currentShotId, 'lines')">
                          <el-icon>
                            <Close />
                          </el-icon>
                        </div>
                      </el-tooltip>
                      <el-tooltip content="保存" placement="top" :effect="'light'">
                        <div class="save-btn"
                          @click.stop="saveEditingDetail(currentShotId, 'lines', currentShotData.sceneId)">
                          <el-icon>
                            <CircleCheck />
                          </el-icon>
                        </div>
                      </el-tooltip>
                    </template>
                  </div>
                </div>
                <div class="dialogue-content-container" v-if="!editingState.details[`${currentShotId}_lines`]">
                  <div v-for="lineObj in currentShotData.lineList" :key="lineObj.id" class="dialogue-item">
                    <div class="dialogue-text-content">
                      <span class="dialogue-character">{{ lineObj.name }}</span>
                      <span class="dialogue-content">{{ lineObj.line }}</span>
                    </div>
                    <!-- 语音播放按钮 -->
                    <el-button v-if="lineObj.voice"
                      :type="isPlayingDialogue(lineObj.id) ? 'success' : 'primary'"
                      size="small"
                      circle
                      @click="playDialogueVoice(lineObj)"
                      class="voice-play-btn-small">
                      <el-icon>
                        <VideoPlay v-if="!isPlayingDialogue(lineObj.id)" />
                        <VideoPause v-else />
                      </el-icon>
                    </el-button>
                  </div>
                </div>
                <div class="dialogue-content-container editing" v-else>
                  <div v-for="(lineObj, i) in tempCurrentShotData.lineList || []" :key="lineObj.id"
                    class="dialogue-item editing">
                    <div class="dialogue-edit-fields">
                      <!-- 角色名称 - 只读显示 -->
                      <div class="readonly-field">
<!--                        <label class="field-label">角色名称:</label>-->
                        <span class="dialogue-character">{{ lineObj.name }}</span>
                      </div>

                      <!-- 对话内容 - 可编辑 -->
                      <div class="editable-field">
<!--                        <label class="field-label">对话内容:</label>-->
                        <el-input v-model="lineObj.line" type="textarea" :autosize="{ minRows: 1, maxRows: 3 }"
                          placeholder="请输入对话内容" class="edit-textarea dialogue-line-input"></el-input>
                      </div>

                      <!-- 角色ID - 只读显示 -->
                      <div class="readonly-field" v-if="false">
                        <label class="field-label">角色ID:</label>
                        <span class="readonly-value">{{ lineObj.charID }}</span>
                      </div>

                      <!-- 语音字段 - 可编辑 -->
                      <div class="editable-field" v-if="false">
                        <label class="field-label">语音文件:</label>
                        <div class="voice-input-container">
                          <el-input v-model="lineObj.voice" placeholder="请输入语音文件URL"
                            class="edit-input voice-input"></el-input>
                          <!-- 语音试听按钮 -->
                          <el-button v-if="lineObj.voice"
                            :type="isPlayingDialogue(lineObj.id) ? 'success' : 'primary'"
                            size="small"
                            circle
                            @click="playDialogueVoice(lineObj)"
                            class="voice-play-btn">
                            <el-icon>
                              <VideoPlay v-if="!isPlayingDialogue(lineObj.id)" />
                              <VideoPause v-else />
                            </el-icon>
                          </el-button>
                        </div>
                      </div>
                    </div>
                    <el-button type="danger" size="small" circle @click="removeLine(currentShotId, i)"
                      class="remove-line-btn" v-if="false">
                      <el-icon>
                        <Close />
                      </el-icon>
                    </el-button>
                  </div>
                  <el-button type="primary" size="small" @click="addLine(currentShotId)" class="add-line-btn" v-if="false">
                    <el-icon>
                      <Plus />
                    </el-icon> 添加对话
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 添加分镜图片预览组件 -->
    <ShotImageViewer v-if="shotPreviewVisible" :visible="shotPreviewVisible" :shot="currentPreviewShot"
      :shots="props.shots" v-model:currentShotId="currentPreviewShotId"
      :title="`${currentPreviewShot?.sceneName} - ${currentPreviewShot?.type || '分镜'}`" @close="closeShotPreview" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, watch, reactive, nextTick } from 'vue'
import { ElTooltip, ElDialog, ElMessage } from 'element-plus'
import {
  VideoPlay,
  VideoPause,
  Edit,
  Picture,
  User,
  ArrowRight,
  ChatDotRound,
  Close,
  CircleCheck,
  Plus,
  Timer,
  Warning
} from '@element-plus/icons-vue'
import ImagePreview from './ImagePreview.vue'
import ShotImageViewer from './ShotImageViewer.vue'

const props = defineProps({
  shots: {
    type: Array,
    default: () => []
  },
  conversationId: {
    type: String,
    default: ''
  },
  columnCount: {
    type: Number,
    default: 1
  },
  imageSize: {
    type: String,
    default: '9:16'
  }
})

const idealCardWidth = ref(props.imageSize === '9:16' ? 150 : 200) // 理想的卡片宽度（像素）

watch(() => props.imageSize, (newVal) => {
  idealCardWidth.value = newVal === '9:16' ? 150 : 200
})

// 添加update:shots事件，用于向父组件传递更新后的数据
const emit = defineEmits(['image-error', 'show-details', 'update:shots'])

// 编辑状态管理
const editingState = reactive({
  shots: {}, // 格式: { shotId: boolean } - 用于分镜卡片中的旁白编辑
  details: {}, // 格式: { shotId_field: boolean } - 用于详情对话框中的字段编辑
})

// 临时存储编辑数据
const tempShots = ref([])

// 标记数据是否已准备好，避免闪烁
const isDataReady = ref(false)

// 监听shots变化，更新临时数据
watch(() => props.shots, (newVal) => {
  if (newVal) {
    tempShots.value = JSON.parse(JSON.stringify(newVal))
    // 当shots数据加载完成后，设置准备就绪状态
    nextTick(() => {
      isDataReady.value = true
    })
  } else {
    isDataReady.value = false
  }
}, { immediate: true, deep: true })

// 分镜详情对话框相关状态
const showingDetails = ref(false)
const currentShotId = ref(null)

// 获取当前选中的分镜数据
const currentShotData = computed(() => {
  if (!currentShotId.value) return null
  return props.shots.find(shot => shot.id === currentShotId.value) || null
})

// 获取当前选中的临时分镜数据
const tempCurrentShotData = computed(() => {
  if (!currentShotId.value) return null
  return tempShots.value.find(shot => shot.id === currentShotId.value) || null
})

// 计算对话框标题
const dialogTitle = computed(() => {
  if (!currentShotData.value) return '分镜详情'
  return `${currentShotData.value.type || '未知类型'} - ID: ${currentShotData.value.id || ''}`
})



// 显示分镜详情
const showShotDetails = (shotId) => {
  currentShotId.value = shotId
  showingDetails.value = true
}

// 隐藏分镜详情
const hideShotDetails = () => {
  showingDetails.value = false
}

// 当前播放的音频元素和ID
const currentAudio = ref(null)
const currentPlayingId = ref(null)

// 对话语音播放相关状态
const currentDialogueAudio = ref(null)
const currentPlayingDialogueId = ref(null)

// 判断是否正在播放特定音色
const isPlaying = (shotId) => {
  return currentPlayingId.value === shotId && currentAudio.value && !currentAudio.value.paused
}

// 判断是否正在播放特定对话语音
const isPlayingDialogue = (lineId) => {
  return currentPlayingDialogueId.value === lineId && currentDialogueAudio.value && !currentDialogueAudio.value.paused
}

// 播放旁白音频
const playVoice = (shot) => {
  // 如果正在播放当前音频，则暂停
  if (isPlaying(shot.id)) {
    currentAudio.value.pause()
    currentAudio.value = null
    currentPlayingId.value = null
    return
  }

  // 如果有其他正在播放的音频，先停止
  if (currentAudio.value) {
    currentAudio.value.pause()
    currentAudio.value = null
    currentPlayingId.value = null
  }

  // 创建新的音频元素并播放
  if (shot.voice) {
    const audioUrl = shot.voice
    const audio = new Audio(audioUrl)

    // 设置事件监听
    audio.addEventListener('play', () => {
      currentPlayingId.value = shot.id
    })

    audio.addEventListener('ended', () => {
      currentAudio.value = null
      currentPlayingId.value = null
    })

    audio.addEventListener('pause', () => {
      if (currentPlayingId.value === shot.id) {
        currentPlayingId.value = null
      }
    })

    audio.addEventListener('error', () => {
      console.error('音频播放错误')
      currentAudio.value = null
      currentPlayingId.value = null
    })

    // 播放
    audio.play().catch(error => {
      console.error('播放失败:', error)
      currentAudio.value = null
      currentPlayingId.value = null
    })

    currentAudio.value = audio
  }
}

// 播放对话语音
const playDialogueVoice = (lineObj) => {
  // 如果正在播放当前音频，则暂停
  if (isPlayingDialogue(lineObj.id)) {
    currentDialogueAudio.value.pause()
    currentDialogueAudio.value = null
    currentPlayingDialogueId.value = null
    return
  }

  // 如果有其他正在播放的对话音频，先停止
  if (currentDialogueAudio.value) {
    currentDialogueAudio.value.pause()
    currentDialogueAudio.value = null
    currentPlayingDialogueId.value = null
  }

  // 创建新的音频元素并播放
  if (lineObj.voice) {
    const audioUrl = lineObj.voice
    const audio = new Audio(audioUrl)

    // 设置事件监听
    audio.addEventListener('play', () => {
      currentPlayingDialogueId.value = lineObj.id
    })

    audio.addEventListener('ended', () => {
      currentDialogueAudio.value = null
      currentPlayingDialogueId.value = null
    })

    audio.addEventListener('pause', () => {
      if (currentPlayingDialogueId.value === lineObj.id) {
        currentPlayingDialogueId.value = null
      }
    })

    audio.addEventListener('error', () => {
      console.error('对话音频播放错误')
      currentDialogueAudio.value = null
      currentPlayingDialogueId.value = null
    })

    // 播放
    audio.play().catch(error => {
      console.error('对话音频播放失败:', error)
      currentDialogueAudio.value = null
      currentPlayingDialogueId.value = null
    })

    currentDialogueAudio.value = audio
  }
}

// 处理图片加载失败
const handleImageError = (shotId) => {
  console.log('处理图片加载失败:', shotId)
  emit('image-error', shotId)
}

// 向输入框插入文本的方法
const insertTextAtCursor = (text) => {
  if (window.insertTextToChatInput && typeof window.insertTextToChatInput === 'function') {
    window.insertTextToChatInput(text)
  }
}

// 发送消息
const insertTextSendMessage = (text) => {
  if (window.insertTextSendMessage && typeof window.insertTextSendMessage === 'function') {
    window.insertTextSendMessage(text)
  }
}

// 开始编辑分镜旁白
const startEditingShot = (shotId) => {
  // 确保tempShots已初始化
  if (!tempShots.value.length && props.shots.length) {
    tempShots.value = JSON.parse(JSON.stringify(props.shots))
  }
  editingState.shots[shotId] = true
}

// 取消编辑分镜旁白
const cancelEditingShot = (shotId) => {
  editingState.shots[shotId] = false
  // 重置临时数据
  if (props.shots.length) {
    tempShots.value = JSON.parse(JSON.stringify(props.shots))
  }
}

// 保存编辑分镜旁白
const saveEditingShot = (shotId, sceneId) => {
  editingState.shots[shotId] = false
  console.log('saveEditingShot', tempShots.value, sceneId)
  let updatedShot = {}
  tempShots.value.forEach(shot => {
    if (shot.id === shotId) {
      updatedShot = shot
    }
  })

  // 提交更新
  emit('update:shots', updatedShot, sceneId)
  // ElMessage.success('保存成功')
}

// 开始编辑详情字段
const startEditingDetail = (shotId, field) => {
  // 确保shotId是字符串而不是ref对象
  const id = typeof shotId === 'object' && 'value' in shotId ? shotId.value : shotId

  const key = `${id}_${field}`
  // 确保tempShots已初始化
  if (!tempShots.value.length && props.shots.length) {
    tempShots.value = JSON.parse(JSON.stringify(props.shots))
  }
  editingState.details[key] = true
}

// 取消编辑详情字段
const cancelEditingDetail = (shotId, field) => {
  // 确保shotId是字符串而不是ref对象
  const id = typeof shotId === 'object' && 'value' in shotId ? shotId.value : shotId

  const key = `${id}_${field}`
  editingState.details[key] = false
  // 重置临时数据
  if (props.shots.length) {
    tempShots.value = JSON.parse(JSON.stringify(props.shots))
  }
}

// 保存编辑详情字段
const saveEditingDetail = (shotId, field, sceneId) => {
  // 确保shotId是字符串而不是ref对象
  const id = typeof shotId === 'object' && 'value' in shotId ? shotId.value : shotId

  const key = `${id}_${field}`
  editingState.details[key] = false
  // 提交更新
  emit('update:shots', tempShots.value, shotId, sceneId)
  ElMessage.success('保存成功')
}

// 添加角色
const addCharacter = (shotId) => {
  // 确保shotId是字符串而不是ref对象
  const id = typeof shotId === 'object' && 'value' in shotId ? shotId.value : shotId

  const shot = tempShots.value.find(s => s.id === id)
  if (shot) {
    if (!shot.characters) {
      shot.characters = []
    }
    shot.characters.push('新角色')
    // 确保能触发tempCurrentShotData的重新计算
    tempShots.value = [...tempShots.value]
  }
}

// 删除角色
const removeCharacter = (shotId, index) => {
  // 确保shotId是字符串而不是ref对象
  const id = typeof shotId === 'object' && 'value' in shotId ? shotId.value : shotId

  const shot = tempShots.value.find(s => s.id === id)
  if (shot && shot.characters) {
    shot.characters.splice(index, 1)
    // 确保能触发tempCurrentShotData的重新计算
    tempShots.value = [...tempShots.value]
  }
}

// 添加对话
const addLine = (shotId) => {
  // 确保shotId是字符串而不是ref对象
  const id = typeof shotId === 'object' && 'value' in shotId ? shotId.value : shotId

  const shot = tempShots.value.find(s => s.id === id)
  if (shot) {
    if (!shot.lineList) {
      shot.lineList = []
    }

    // 获取下一个可用的ID
    const nextId = shot.lineList.length > 0 ? Math.max(...shot.lineList.map(line => line.id)) + 1 : 1

    // 添加新对话对象
    const newLine = {
      name: "新角色",
      line: "请输入对话内容...",
      charID: "Role-01",
      voice: "", // 新增语音字段
      id: nextId
    }

    shot.lineList.push(newLine)

    // 确保能触发tempCurrentShotData的重新计算
    tempShots.value = [...tempShots.value]
  }
}

// 删除对话
const removeLine = (shotId, index) => {
  // 确保shotId是字符串而不是ref对象
  const id = typeof shotId === 'object' && 'value' in shotId ? shotId.value : shotId

  const shot = tempShots.value.find(s => s.id === id)
  if (shot && shot.lineList && Array.isArray(shot.lineList)) {
    // 删除指定索引的对话
    if (index >= 0 && index < shot.lineList.length) {
      shot.lineList.splice(index, 1)
      // 确保能触发tempCurrentShotData的重新计算
      tempShots.value = [...tempShots.value]
    }
  }
}

// 添加预览相关状态
const shotPreviewVisible = ref(false)
const currentPreviewShot = ref(null)
const currentPreviewShotId = ref(null)

// 打开分镜图片预览
const openShotPreview = (shot) => {
  currentPreviewShot.value = shot
  currentPreviewShotId.value = shot.id
  shotPreviewVisible.value = true
}

// 关闭分镜图片预览
const closeShotPreview = () => {
  shotPreviewVisible.value = false
}

// 监听预览中的分镜ID变化，更新当前预览的分镜数据
watch(() => currentPreviewShotId.value, (newShotId) => {
  if (newShotId && props.shots && props.shots.length) {
    const newShot = props.shots.find(shot => shot.id === newShotId)
    if (newShot) {
      currentPreviewShot.value = newShot
    }
  }
})

// 组件挂载后初始化
onMounted(() => {
  // 简化初始化逻辑，不再需要计算列数
})

// 在组件卸载前清理
onBeforeUnmount(() => {
  // 清理音频
  if (currentAudio.value) {
    currentAudio.value.pause()
    currentAudio.value = null
  }

  // 清理对话音频
  if (currentDialogueAudio.value) {
    currentDialogueAudio.value.pause()
    currentDialogueAudio.value = null
  }

  // 重置数据准备状态
  isDataReady.value = false
})

// 格式化毫秒为秒，保留一位小数
const formatDuration = (milliseconds) => {
  if (milliseconds === undefined || milliseconds === null || isNaN(milliseconds)) return '未设置'
  return (milliseconds / 1000).toFixed(1) + ' 秒'
}

// 检查分镜是否缺少图片、旁白或声音
const isShotIncomplete = (shot) => {
  return (!shot.image || shot.imageStatus === 'ERROR') || 
         !shot.lineList || shot.lineList.length === 0 || shot.lineList.every(line => !line.voice);
}

// 获取分镜缺少的内容的提示信息
const getShotWarningMessage = (shot) => {
  let message = '缺少: ';
  
  if (!shot.image || shot.imageStatus === 'ERROR') {
    message += '图片 ';
  }
  
  if (!shot.narration) {
    message += '旁白 ';
  }
  
  if (!shot.lineList || shot.lineList.length === 0 || shot.lineList.every(line => !line.voice)) {
    message += '声音';
  }
  
  return message.trim();
}
</script>

<style scoped>
/* 场景内容样式 */
.scene-content {
  position: relative;
}


.voice-yinxiao-tag {
  color: #409eff;
  border: 1px solid #409eff;
  opacity: 1;
  padding: 0px 4px;
  border-radius: 4px;
  font-size: 12px;
  margin-right: 4px;
}

body.dark .voice-yinxiao-tag {
  color: #409eff;
  border: 1px solid #409eff;
}

/* CSS Grid 布局样式 */
.shots-grid {
  width: 100%;
}

:deep(.el-overlay) {
  backdrop-filter: blur(10px);
}

/* Grid 容器样式 */
.grid-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 8px;
  width: 100%;
}

/* 骨架屏样式 */
.skeleton-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 8px;
  width: 100%;
  padding: 8px 0;
}

.skeleton-card {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 12px #6365f152;
  overflow: hidden;
  padding: 16px;
  animation: skeleton-pulse 1.5s infinite;
  height: 220px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.skeleton-header {
  height: 24px;
  background-color: #f0f0f0;
  border-radius: 4px;
  margin-bottom: 12px;
  width: 70%;
}

.skeleton-content {
  flex: 1;
  background-color: #f5f5f5;
  border-radius: 4px;
}

@keyframes skeleton-pulse {
  0% {
    opacity: 0.6;
  }

  50% {
    opacity: 1;
  }

  100% {
    opacity: 0.6;
  }
}

body.dark .skeleton-card {
  background-color: var(--bg-card);
}

body.dark .skeleton-header {
  background-color: rgba(60, 60, 60, 0.5);
}

body.dark .skeleton-content {
  background-color: rgba(50, 50, 50, 0.5);
}

.shot-card {
  background-color: #ffffff7c;;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.25, 1, 0.5, 1);
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 12px #6365f152;
  animation: fadeIn 0.3s ease;
}

body.dark .shot-card {
  background-color: #16161879;
}

.shot-card:hover {
  transform: scale(1.02);
  box-shadow: 0 2px 12px #6365f152;
  border: 2px solid #6365f1;
}

.shot-card:hover img {
  transform: scale(1.04);
}

.shot-main {
  position: relative;
  display: flex;
  flex-direction: column;
  flex: 1;
  border-radius: 8px;
  overflow: hidden;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(5px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式布局 */
@media (max-width: 768px) {
  .grid-container {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 10px;
  }
  
  .skeleton-container {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 10px;
  }
}

@media (max-width: 480px) {
  .grid-container {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .skeleton-container {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .shot-main {
    flex-direction: column;
  }

  .shot-image-container {
    width: 100%;
    max-height: none;
  }

  .shot-content-container {
    width: 100%;
  }

  .detail-layout {
    flex-direction: column;
  }

  .detail-image-container {
    flex: 0 0 auto;
    width: 100%;
    height: 250px;
    margin-bottom: 16px;
  }

  :deep(.el-dialog) {
    width: 90% !important;
    margin: 5vh auto !important;
  }
}

/* 图片容器样式 */
.shot-image-container {
  width: 100%;
  /* min-height: 172px; */
  flex-shrink: 0;
  position: relative;
  /* 为绝对定位的子元素创建参考点 */
}

/* 图片预览包装器样式 */
.image-preview-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
}

/* 分镜ID标签样式 */
.shot-id-tag {
  position: absolute;
  top: 0px;
  left: 0px;
  background-color: rgba(99, 101, 241, 0.77);
  color: white;
  font-size: 10px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 0 0 6px 0;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  z-index: 1;
  /* 确保标签显示在图片上方 */
}

/* 警告图标样式 */
.shot-warning-tag {
  position: absolute;
  width: 18px;
  height: 18px;
  top: 6px;
  right: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(246, 4, 4, 1);
  color: white;
  font-size: 16px;
  padding: 4px;
  border-radius: 50%;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  z-index: 1;
  cursor: pointer;
}

.shot-warning-tag:hover {
  transform: scale(1.1);
  background-color: rgba(245, 158, 11, 1);
}

/* 持续时间标签样式 */
.shot-duration-tag {
  position: absolute;
  top: -26px;
  left: 6px;
  background-color: rgb(56, 149, 192);
  color: white;
  font-size: 10px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 6px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  z-index: 1;
  /* 确保标签显示在图片上方 */
}

.shot-id-tag:hover {
  transform: scale(1.05);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
}

.shot-duration-tag:hover {
  transform: scale(1.05);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
}

body.dark .shot-duration-tag {
  background-color: rgba(5, 117, 168, 0.829);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
}

/* 内容容器样式 */
.shot-content-container {
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;
  /* padding: 10px 15px; */
  /* background-color: #fff; */
  gap: 10px;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

body.dark .shot-content-container {
  /* background-color: var(--bg-card); */
}

/* 对话样式 */
.shot-dialogue {
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  margin: 0 0 0 0;
  padding: 8px;
  /* background-color: #f8fafc; */
  /* border-left: 1px solid #6366f1; */
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: center;
  transition: all 0.3s ease;
}

.pangbai {
  border-radius: 0px 10px 10px 0px;
}

.duihua {
  border-radius: 0px 0px 12px 12px;
  border-top: 1px solid #e2e8f0;
}

body.dark .duihua {
  border-top: 1px solid #e2e8f023;
}

body.dark .shot-dialogue {
  /* background-color: var(--bg-tertiary); */
}

.dialogue-text {
  flex: 1;
  font-size: 14px;
  color: #1e293b;
  transition: color 0.3s;
  width: 100%;

  /* display: -webkit-box;
  line-height: 1.6;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  box-sizing: border-box;
  max-height: calc(1.6em * 2); */
}

body.dark .dialogue-text {
  color: var(--text-secondary);
}

.dialogue-content {
  flex: 1;
  text-align: left;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  /* height: calc(1.6em * 2); */
}

/* 播放按钮样式 */
.voice-play {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: rgba(64, 160, 255, 0.715);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex-shrink: 0;
  position: relative;
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.3);
  cursor: pointer;
}

.voice-play:hover {
  transform: scale(1.1);
  background-color: #409eff;
  box-shadow: 0 4px 10px rgba(64, 158, 255, 0.4);
}

.play-icon {
  width: 18px;
  height: 18px;
}

/* 播放状态 */
.voice-play.playing {
  background-color: #67c23a;
  box-shadow: 0 2px 6px rgba(103, 194, 58, 0.3);
}

.voice-play.playing:hover {
  background-color: #85ce61;
  box-shadow: 0 4px 10px rgba(103, 194, 58, 0.4);
}

/* 波形动画 */
.wave-animation {
  position: absolute;
  left: 50%;
  top: -14px;
  transform: translateX(-50%);
  display: flex;
  align-items: flex-end;
  height: 12px;
  width: 14px;
  gap: 2px;
}

.wave-bar {
  width: 2px;
  background-color: #67c23a;
  border-radius: 1px;
  animation: waveAnimation 0.8s infinite ease-in-out;
}

.wave-bar:nth-child(1) {
  height: 5px;
  animation-delay: 0s;
}

.wave-bar:nth-child(2) {
  height: 8px;
  animation-delay: 0.2s;
}

.wave-bar:nth-child(3) {
  height: 6px;
  animation-delay: 0.4s;
}

.wave-bar:nth-child(4) {
  height: 7px;
  animation-delay: 0.6s;
}

@keyframes waveAnimation {

  0%,
  100% {
    transform: scaleY(0.6);
  }

  50% {
    transform: scaleY(1);
  }
}

body.dark .voice-play {
  background-color: rgba(64, 160, 255, 0.3);
}

body.dark .voice-play:hover {
  background-color: rgba(64, 160, 255, 0.8);
}

body.dark .voice-play.playing {
  background-color: rgba(103, 194, 58, 0.6);
}

body.dark .voice-play.playing:hover {
  background-color: rgba(103, 194, 58, 0.8);
}

/* 图标按钮样式 */
.scene-group-icon-btn {
  padding: 4px 10px;
  border-radius: 10px;
  background-color: #8d8ff0;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(99, 102, 241, 0.3);
}

.scene-group-icon-btn:hover {
  background-color: #6366f1;
  transform: scale(1.1);
  box-shadow: 0 3px 8px rgba(99, 102, 241, 0.5);
}

body.dark .scene-group-icon-btn {
  background-color: rgba(99, 102, 241, 0.7);
  box-shadow: 0 2px 6px rgba(99, 102, 241, 0.2);
}

body.dark .scene-group-icon-btn:hover {
  background-color: rgba(99, 102, 241, 0.9);
  box-shadow: 0 3px 8px rgba(99, 102, 241, 0.4);
}

/* 分镜详情对话框样式 */
/* 覆盖 Element Plus 对话框样式 */
:deep(.el-dialog) {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  padding: 0px;
}

:deep(.el-dialog__header) {
  margin: 0;
  padding: 16px 20px;
  background: linear-gradient(135deg, #6366f1cc, #4f46e5cc);
  border-bottom: none;
}

body.dark :deep(.el-dialog__header) {
  background: linear-gradient(135deg, #4f46e5cc, #3730a3cc);
  border-color: var(--border-color);
}

:deep(.el-dialog__title) {
  font-size: 16px;
  font-weight: 600;
  color: white;
  letter-spacing: 0.5px;
}

body.dark :deep(.el-dialog__title) {
  color: white;
}

:deep(.el-dialog__body) {
  padding: 0;
  background-color: white;
}

body.dark :deep(.el-dialog__body) {
  background-color: var(--bg-card);
}

:deep(.el-dialog__headerbtn) {
  top: 16px;
  right: 16px;
}

:deep(.el-dialog__headerbtn .el-dialog__close) {
  color: rgba(255, 255, 255, 0.8);
}

:deep(.el-dialog__headerbtn:hover .el-dialog__close) {
  color: white;
}

/* 分镜详情对话框内容样式 */
.detail-dialog-content {
  /* padding-bottom: 16px; */
  background-color: #fff;
}

body.dark .detail-dialog-content {
  background-color: var(--bg-card);
}

.scene-info-bar {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

body.dark .scene-info-bar {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
}

.scene-info-label {
  font-size: 14px;
  font-weight: 600;
  color: #64748b;
  margin-right: 8px;
}

body.dark .scene-info-label {
  color: var(--text-tertiary);
}

.scene-info-value {
  font-size: 14px;
  color: #1e293b;
  font-weight: 500;
}

body.dark .scene-info-value {
  color: var(--text-secondary);
}

.detail-layout {
  display: flex;
  gap: 20px;
  justify-content: center;
  align-items: start;
  padding: 30px;
}

.detail-image-container {
  flex: 1;
  height: 420px;
  overflow: hidden;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.detail-image {
  height: 100%;
  object-fit: contain;
  object-position: center;
  border-radius: 12px;
}

body.dark .detail-image-container {
  background-color: var(--bg-tertiary);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.detail-content {
  width: 400px;
  /* 防止内容溢出 */
}

.detail-card {
  margin-bottom: 16px;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  gap: 4px;
  background-color: white;
}

body.dark .detail-card {
  background-color: var(--bg-card);
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #4f46e5;
  font-weight: 600;
}

body.dark .detail-label {
  color: var(--text-tertiary);
}

.detail-value {
  font-size: 14px;
  color: #1e293b;
  line-height: 1.5;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  text-align: left;
  padding: 8px 12px;
  background-color: #6666660b;
  border-radius: 8px;
}

body.dark .detail-value {
  color: var(--text-secondary);
}

/* 多值标签样式 */
.multi-value-item {
  margin-bottom: 4px;
}

.multi-value-tag {
  display: inline-block;
  padding: 5px 10px;
  border-radius: 6px;
  background-color: #e2e8f0;
  color: #1e293b;
  font-size: 13px;
  white-space: nowrap;
  transition: all 0.3s ease;
}

body.dark .multi-value-tag {
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
}

.character-tag {
  background-color: #dbeafe;
  color: #1e40af;
}

body.dark .character-tag {
  background-color: rgba(219, 234, 254, 0.2);
  color: #93c5fd;
}

.character-list-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.empty-characters {
  color: #94a3b8;
  font-size: 14px;
  font-style: italic;
}

/* 对话内容样式 */
.dialogue-card {
  /* border: 1px solid #e2e8f0; */
}

body.dark .dialogue-card {
  /* border: 1px solid var(--border-color); */
}

.dialogue-label {
  /* margin-bottom: 12px; */
  color: #4f46e5;
}

body.dark .dialogue-label {
  color: #818cf8;
}

.dialogue-content-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.dialogue-content-container .dialogue-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  padding: 8px 12px;
  background-color: #f8fafc;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.dialogue-text-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

body.dark .dialogue-content-container .dialogue-item {
  background-color: var(--bg-tertiary);
}

.dialogue-content-container .dialogue-character {
  font-weight: 600;
  color: #3b82f6;
  font-size: 14px;
  text-align: left;
}

body.dark .dialogue-content-container .dialogue-character {
  color: #60a5fa;
}

.dialogue-content-container .dialogue-content {
  font-size: 14px;
  color: #1e293b;
  line-height: 1.6;
}

body.dark .dialogue-content-container .dialogue-content {
  color: var(--text-secondary);
}

/* 编辑按钮样式 */
.field-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: auto;
}

.shot-actions {
  position: absolute;
  right: 10px;
  bottom: 10px;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
  margin-left: auto;
  margin-bottom: auto;
  opacity: 0;
  /* 默认隐藏 */
  transition: opacity 0.3s ease;
  /* 添加过渡效果 */
}

.shot-dialogue:hover .shot-actions {
  opacity: 1;
  /* 鼠标悬停时显示 */
}

.edit-btn,
.cancel-btn,
.save-btn {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.edit-btn.small,
.cancel-btn.small,
.save-btn.small {
  width: 24px;
  height: 24px;
  font-size: 12px;
}

.edit-btn {
  background-color: #6366f1;
  color: white;
}

.edit-btn:hover {
  background-color: #4f46e5;
  transform: scale(1.1);
}

.cancel-btn {
  background-color: #f87171;
  color: white;
}

.cancel-btn:hover {
  background-color: #ef4444;
  transform: scale(1.1);
}

.save-btn {
  background-color: #10b981;
  color: white;
}

.save-btn:hover {
  background-color: #059669;
  transform: scale(1.1);
}

/* 编辑输入框样式 */
.edit-input {
  margin-top: 4px;
  width: 100%;
}

.edit-textarea {
  margin-top: 4px;
  width: 100%;
}

.character-input {
  width: 120px;
}

.add-character-btn,
.remove-character-btn,
.add-line-btn,
.remove-line-btn {
  margin-left: 8px;
}

.multi-value-item.editing {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.character-list-container.editing {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.dialogue-item.editing {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  margin-bottom: 8px;
}

.dialogue-content-container.editing {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding-left: 0;
}

.dialogue-edit-fields {
  display: flex;
  width: 100%;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.character-name-input {
  width: 100%;
}

.dialogue-line-input {
  width: 100%;
}

.char-id-input {
  width: 100%;
}

/* 只读字段样式 */
.readonly-field {
  display: flex;
  flex-direction: row;
  text-align: left;
  gap: 4px;
}

.editable-field {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
  border-radius: 8px;
  background-color: #fff;
  border: 1px solid #e2e8f0;
}

body.dark .editable-field {
  background-color: var(--bg-secondary);
  border: 1px solid #1e293b;
}

.field-label {
  font-size: 12px;
  font-weight: 600;
  color: #64748b;
}

body.dark .field-label {
  color: var(--text-tertiary);
}

.readonly-value {
  padding: 8px 12px;
  background-color: #f1f5f9;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  color: #475569;
  font-size: 14px;
}

body.dark .readonly-value {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
  color: var(--text-secondary);
}

/* 语音输入容器样式 */
.voice-input-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.voice-input {
  flex: 1;
}

.voice-play-btn {
  flex-shrink: 0;
  width: 32px;
  height: 32px;
}

.voice-play-btn-small {
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  font-size: 12px;
}

/* Element Plus 组件样式覆盖 */
:deep(.el-input__inner) {
  border-radius: 8px;
}

:deep(.el-textarea__inner) {
  border-radius: 8px;
  resize: none;
}

body.dark :deep(.el-input__inner),
body.dark :deep(.el-textarea__inner) {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

body.dark :deep(.el-input__inner:focus),
body.dark :deep(.el-textarea__inner:focus) {
  border-color: #6366f1;
}
</style>