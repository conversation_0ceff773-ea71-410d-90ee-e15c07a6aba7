import axios from 'axios'

// 创建API请求实例
const apiClient = axios.create({
  baseURL: '/api',
  timeout: 30000,
  headers: {
    'Accept': '*/*',
    'Accept-Language': 'zh-CN',
    'User-Agent': 'VueApp'
  }
})

// 添加请求拦截器，在每个请求中添加token和时间戳
apiClient.interceptors.request.use(
  config => {
    // 从localStorage获取token
    const accessToken = localStorage.getItem('token') || ''
    
    // 生成当前时间戳
    const timestamp = Date.now()
    
    config.headers.accessToken = accessToken
    config.headers.timestamp = timestamp
    
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

/**
 * 获取可用的AI模型列表
 * @param {string} type - 模型类型 ('image' | 'video')
 * @returns {Promise} - 返回模型列表数据
 */
export function getAvailableModels(type = 'image') {
  return apiClient.get('/agent/models/list', {
    params: { type }
  })
}

/**
 * 文本生成图片
 * @param {Object} params - 生成参数
 * @param {string} params.prompt - 提示词
 * @param {string} params.model - 模型名称
 * @param {string} params.aspectRatio - 宽高比
 * @param {number} params.batchSize - 生成数量
 * @param {string} [params.style] - 风格
 * @param {number} [params.seed] - 随机种子
 * @returns {Promise} - 返回生成任务ID
 */
export function generateImageFromText(params) {
  return apiClient.post('/agent/creation/text-to-image', params)
}

/**
 * 图片编辑生成
 * @param {Object} params - 编辑参数
 * @param {string} params.prompt - 提示词
 * @param {string} params.model - 模型名称
 * @param {string} params.sourceImageUrl - 源图片URL
 * @param {string} params.mode - 编辑模式 ('edit' | 'variation' | 'upscale')
 * @param {number} [params.strength] - 编辑强度 (0.1-1.0)
 * @param {string} [params.maskImageUrl] - 遮罩图片URL (局部编辑时使用)
 * @returns {Promise} - 返回生成任务ID
 */
export function generateImageFromImage(params) {
  return apiClient.post('/agent/creation/image-to-image', params)
}

/**
 * 多图融合生成
 * @param {Object} params - 融合参数
 * @param {string} params.prompt - 提示词
 * @param {string} params.model - 模型名称
 * @param {Array<string>} params.sourceImageUrls - 源图片URL数组
 * @param {Array<number>} [params.weights] - 图片权重数组
 * @returns {Promise} - 返回生成任务ID
 */
export function generateImageFromMultipleImages(params) {
  return apiClient.post('/agent/creation/multi-image-fusion', params)
}

/**
 * 文本生成视频
 * @param {Object} params - 生成参数
 * @param {string} params.prompt - 提示词
 * @param {string} params.model - 模型名称
 * @param {string} params.aspectRatio - 宽高比
 * @param {number} params.duration - 视频时长(秒)
 * @param {number} [params.fps] - 帧率
 * @param {string} [params.style] - 风格
 * @returns {Promise} - 返回生成任务ID
 */
export function generateVideoFromText(params) {
  return apiClient.post('/agent/creation/text-to-video', params)
}

/**
 * 图片生成视频
 * @param {Object} params - 生成参数
 * @param {string} params.prompt - 提示词
 * @param {string} params.model - 模型名称
 * @param {string} params.sourceImageUrl - 源图片URL
 * @param {number} params.duration - 视频时长(秒)
 * @param {string} [params.motionStrength] - 运动强度
 * @returns {Promise} - 返回生成任务ID
 */
export function generateVideoFromImage(params) {
  return apiClient.post('/agent/creation/image-to-video', params)
}

/**
 * 查询生成任务状态
 * @param {string} taskId - 任务ID
 * @returns {Promise} - 返回任务状态和结果
 */
export function getGenerationTaskStatus(taskId) {
  return apiClient.get(`/agent/creation/task/${taskId}/status`)
}

/**
 * 获取生成历史
 * @param {Object} params - 查询参数
 * @param {string} params.type - 类型 ('image' | 'video')
 * @param {number} [params.page] - 页码
 * @param {number} [params.pageSize] - 每页数量
 * @returns {Promise} - 返回历史记录
 */
export function getGenerationHistory(params) {
  return apiClient.get('/agent/creation/history', { params })
}

/**
 * 删除生成记录
 * @param {string} recordId - 记录ID
 * @returns {Promise} - 返回删除结果
 */
export function deleteGenerationRecord(recordId) {
  return apiClient.delete(`/agent/creation/record/${recordId}`)
}

/**
 * 收藏/取消收藏生成结果
 * @param {string} recordId - 记录ID
 * @param {boolean} favorite - 是否收藏
 * @returns {Promise} - 返回操作结果
 */
export function toggleFavoriteRecord(recordId, favorite) {
  return apiClient.post(`/agent/creation/record/${recordId}/favorite`, { favorite })
}

export default {
  getAvailableModels,
  generateImageFromText,
  generateImageFromImage,
  generateImageFromMultipleImages,
  generateVideoFromText,
  generateVideoFromImage,
  getGenerationTaskStatus,
  getGenerationHistory,
  deleteGenerationRecord,
  toggleFavoriteRecord
}
