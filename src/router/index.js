import { createRouter, createWebHistory } from 'vue-router'
import { BASE_PATH } from '../api/config'
import Login from '../views/Login.vue'
import Home from '../views/Home.vue'
import Create from '../views/Create.vue'
import InputSection from '../views/InputSection.vue'
import Stories from '../views/Stories.vue'
import GenerationRecords from '../views/GenerationRecords.vue'
import Profile from '../views/Profile.vue'
import InviteActivation from '../views/InviteActivation.vue'
import Membership from '../views/Membership.vue'
import BindMobile from '../views/BindMobile.vue'
import Assets from '../views/Assets.vue'
import CanvasEditor from '../views/CanvasEditor.vue'
import ImageStudio from '../views/ImageStudio.vue'
import VideoStudio from '../views/VideoStudio.vue'
import AudioStudio from '../views/AudioStudio.vue'

const routes = [
  {
    path: '/',
    redirect: '/home'
  },
  {
    path: '/login',
    name: 'Login',
    component: Login
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('../views/Register.vue')
  },
  {
    path: '/bind-mobile',
    name: 'BindMobile',
    component: BindMobile,
    meta: { requiresAuth: true }
  },
  {
    path: '/home',
    name: 'Home',
    component: Home
  },
  {
    path: '/create',
    name: 'Create',
    component: Create,
    meta: { requiresAuth: true }
  },
  {
    path: '/inputSection',
    name: 'InputSection',
    component: InputSection,
    meta: { requiresAuth: true }
  },
  {
    path: '/stories',
    name: 'Stories',
    component: Stories,
    meta: { requiresAuth: true }
  },
  {
    path: '/generation-records',
    name: 'GenerationRecords',
    component: GenerationRecords,
    meta: { requiresAuth: true }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: Profile,
    meta: { requiresAuth: true }
  },
  {
    path: '/neo-assets',
    name: 'NeoAssets',
    component: Assets,
    meta: { requiresAuth: true }
  },
  {
    path: '/inviteActivate',
    name: 'InviteActivation',
    component: InviteActivation,
    meta: { requiresAuth: true }
  },
  {
    path: '/membership',
    name: 'Membership',
    component: Membership,
    meta: { requiresAuth: true }
  },
  {
    path: '/canvas-editor',
    name: 'CanvasEditor',
    component: CanvasEditor,
    meta: { requiresAuth: true }
  },
  {
    path: '/share/:id?',
    name: 'Share',
    component: () => import('../views/Share.vue'),
    // 不设置requiresAuth，表示不需要登录即可访问
  },
  {
    path: '/featured-works',
    name: 'FeaturedWorks',
    component: () => import('../views/FeaturedWorks.vue')
  },
  {
    path: '/creator/:creatorId?',
    name: 'CreatorProfile',
    component: () => import('../views/CreatorProfile.vue')
  },
  {
    path: '/video-editor',
    name: 'VideoEditor',
    component: () => import('../views/VideoEditor.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/image-studio',
    name: 'ImageStudio',
    component: ImageStudio,
    meta: { requiresAuth: true }
  },
  {
    path: '/video-studio',
    name: 'VideoStudio',
    component: VideoStudio,
    meta: { requiresAuth: true }
  },
  {
    path: '/audio-studio',
    name: 'AudioStudio',
    component: AudioStudio,
    meta: { requiresAuth: true }
  },
  {
    path: '/test/dropdown-portal',
    name: 'DropdownPortalTest',
    component: () => import('../components/test/DropdownPortalTest.vue')
  },
  {
    path: '/test/image-preview',
    name: 'ImagePreviewTest',
    component: () => import('../components/test/ImagePreviewTest.vue')
  }
]

const router = createRouter({
  history: createWebHistory(BASE_PATH),
  routes,
  // 添加scrollBehavior配置，控制页面滚动行为
  scrollBehavior(to, from, savedPosition) {
    // 如果有保存的位置（比如用户点击了浏览器的后退按钮），则使用保存的位置
    if (savedPosition) {
      return savedPosition;
    }
    // 否则滚动到页面顶部
    return { top: 0 };
  }
})

// 导航守卫
router.beforeEach((to, from, next) => {
  console.log(to, from, next)
  console.log("inviteCode",to.query.inviteCode)

  if(to.name === 'InviteActivation' && to.query.inviteCode){
    localStorage.setItem('inviteCode', to.query.inviteCode)
  }

  const token = localStorage.getItem('token')
  if (to.meta.requiresAuth && !token && to.name !== 'Home') {
    next('/login')
  } else {
    next()
  }
})

export default router 