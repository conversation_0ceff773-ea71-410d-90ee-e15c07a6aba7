/* 暗色主题变量 */
:root.dark {
  --bg-primary: #1a1a1a;
  --bg-secondary: #2424249a;
  --bg-tertiary: #1e1e1e;
  --bg-card: #161618;
  --bg-hover: #333333;
  --bg-secondary-video: #1f2229;
  
  --text-primary: #f0f0f0;
  --text-secondary: #a0a0a0;
  --text-tertiary: #808080;
  
  --border-color: #1f1f1f;
  --border-light: #444444;
  --border-color-hover: #505050;
  
  --primary-color: #6366f1;
  --primary-light: rgba(99, 102, 241, 0.2);
  --primary-hover: #5558dd;
  
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  
  --shadow-color: rgba(0, 0, 0, 0.3);
  --shadow-light: rgba(0, 0, 0, 0.2);
  --shadow-subtle: rgba(0, 0, 0, 0.1);
  
  --scrollbar-thumb-color: rgba(128, 128, 128, 0.5);
  --scrollbar-thumb-hover-color: rgba(128, 128, 128, 0.7);
  --white: #ffffff;
}

/* 应用暗色主题 */
body.dark {
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

body.dark #app {
  background-color: var(--bg-primary);
}

/* 卡片样式 */
body.dark .el-card {
  background-color: var(--bg-card);
  border-color: var(--border-color);
  color: var(--text-primary);
  box-shadow: 0 2px 12px var(--shadow-light);
}

/* 输入框样式 */
body.dark .el-input__wrapper {
  background-color: var(--bg-secondary);
  box-shadow: 0 0 0 1px var(--border-color) inset;
}

body.dark .el-select__wrapper{
  background-color: var(--bg-secondary);
  box-shadow: 0 0 0 1px var(--border-color) inset;
}

body.dark is-focused{
  box-shadow: 0 0 0 1px var(--primary-color) inset !important;
}

body.dark .el-input__wrapper.is-focus {
  box-shadow: 0 0 0 1px var(--primary-color) inset !important;
}

body.dark .el-input__inner {
  color: var(--text-primary);
  /* background-color: var(--bg-secondary); */
}

body.dark .el-input__prefix-icon {
  color: var(--text-tertiary);
}

body.dark .el-input-group__append,
body.dark .el-input-group__prepend {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
  color: var(--text-secondary);
}

/* 文本域样式 */
body.dark .el-textarea__inner {
  /* background-color: var(--bg-secondary); */
  background-color: transparent !important;
  color: var(--text-primary);
  /* border-color: var(--border-color); */
  /* color: var(--text-primary); */
}

body.dark .el-textarea__inner:focus {
  /* border-color: var(--primary-color); */
}

body.dark .el-textarea__inner::placeholder,
body.dark .el-input__inner::placeholder {
  color: var(--text-tertiary);
}

/* 标签样式 */
body.dark .el-tag {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
  color: var(--text-secondary);
}

body.dark .el-tag--dark {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: var(--white);
}

body.dark .el-tag.el-tag--primary {
  --el-tag-bg-color: rgba(64, 158, 255, 0.2);
  --el-tag-border-color: rgba(64, 158, 255, 0.3);
  --el-tag-hover-color: var(--primary-color);
  --el-tag-text-color: #67a9ff;
}

body.dark .el-tag.el-tag--success {
  --el-tag-bg-color: rgba(103, 194, 58, 0.2);
  --el-tag-border-color: rgba(103, 194, 58, 0.3);
  --el-tag-hover-color: #67c23a;
  --el-tag-text-color: #85ce61;
}

body.dark .el-tag.el-tag--warning {
  --el-tag-bg-color: rgba(230, 162, 60, 0.2);
  --el-tag-border-color: rgba(230, 162, 60, 0.3);
  --el-tag-hover-color: #e6a23c;
  --el-tag-text-color: #eebe77;
}

body.dark .el-tag.el-tag--danger {
  --el-tag-bg-color: rgba(245, 108, 108, 0.2);
  --el-tag-border-color: rgba(245, 108, 108, 0.3);
  --el-tag-hover-color: #f56c6c;
  --el-tag-text-color: #f89898;
}

/* 下拉菜单样式 */
body.dark .el-dropdown-menu {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
}

body.dark .el-dropdown-menu__item {
  color: var(--text-primary);
}

body.dark .el-dropdown-menu__item:hover, 
body.dark .el-dropdown-menu__item:focus {
  background-color: var(--bg-tertiary);
  color: var(--primary-color);
}

/* 按钮样式 */
body.dark .el-button {
  --el-button-hover-bg-color: var(--bg-tertiary);
  --el-button-hover-text-color: var(--text-primary);
  --el-button-hover-border-color: var(--border-color-hover);
}

body.dark .el-button--default {
  --el-button-bg-color: var(--bg-secondary);
  --el-button-text-color: var(--text-primary);
  --el-button-border-color: var(--border-color);
}

body.dark .el-button--primary {
  --el-button-bg-color: var(--primary-color);
  --el-button-border-color: var(--primary-color);
}

body.dark .el-button:hover,
body.dark .el-button:focus {
  background-color: var(--bg-hover);
  border-color: var(--border-light);
}

/* 对话框样式 */
body.dark .el-dialog {
  background-color: var(--bg-card);
  border-color: var(--border-color);
}

body.dark .el-dialog__title {
  color: var(--text-primary);
}

body.dark .el-dialog__header {
  border-bottom-color: var(--border-color);
}

body.dark .el-dialog__footer {
  border-top-color: var(--border-color);
}

/* Stories.vue 页面特定样式 */
body.dark .stories-page {
  background-color: var(--bg-primary);
}

body.dark .story-card {
  /* background-color: var(--bg-card); */
}

body.dark .story-title {
  color: var(--text-primary);
}

body.dark .story-description {
  color: var(--text-secondary);
}

body.dark .story-date {
  color: var(--text-tertiary);
}

body.dark .story-image-container {
  background-color: var(--bg-tertiary);
}

body.dark .stories-footer {
  background-color: var(--bg-secondary);
  border-top-color: var(--border-color);
  color: var(--text-secondary);
}

/* 空状态样式 */
body.dark .el-empty__description {
  color: var(--text-secondary);
}

/* 删除对话框样式 */
body.dark .delete-dialog-content {
  color: var(--text-primary);
}

body.dark .delete-warning-title {
  color: var(--text-primary);
}

body.dark .delete-warning-desc {
  color: var(--text-secondary);
}

body.dark .cancel-button {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
}

body.dark .cancel-button:hover {
  background-color: var(--bg-hover);
  border-color: var(--border-light);
}

/* 工具提示样式 */
body.dark .el-tooltip__popper {
  background-color: var(--bg-card);
  color: var(--text-primary);
  border-color: var(--border-color);
}

/* 加载骨架屏样式 */
body.dark .el-skeleton {
  --el-skeleton-color: var(--bg-tertiary);
  --el-skeleton-to-color: var(--bg-hover);
}

/* 开关主题样式 */
.theme-switch {
  z-index: 100;
  display: flex;
  align-items: center;
  padding: 8px;
  border-radius: 30px;
  transition: all 0.3s ease;
}

.app-header .theme-switch {
  position: static;
  right: auto;
  top: auto;
}

.theme-switch:hover {
  background-color: rgba(0, 0, 0, 0.3);
}

body.dark .theme-switch {
  background-color: rgba(255, 255, 255, 0.1);
}

body.dark .theme-switch:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.theme-icon {
  font-size: 18px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #333;
}

body.dark .theme-icon {
  color: var(--text-primary);
}

.theme-icon.active {
  color: var(--primary-color);
  transform: scale(1.2);
}

/* 滚动条样式 */
body.dark .el-scrollbar__thumb {
  background-color: var(--scrollbar-thumb-color);
}

body.dark .el-scrollbar__thumb:hover {
  background-color: var(--scrollbar-thumb-hover-color);
}

/* 复选框样式 */
body.dark .el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

body.dark .el-checkbox__inner {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
}

body.dark .el-checkbox__label {
  color: var(--text-primary);
}

/* 选择器样式 */
body.dark .el-select {
  --el-select-input-color: var(--text-primary);
  --el-select-input-focus-border-color: var(--primary-color);
}

body.dark .el-select .el-input__wrapper {
  background-color: var(--bg-secondary);
}

body.dark .el-select-dropdown {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
}

body.dark .el-select-dropdown__item {
  color: var(--text-primary);
}

body.dark .el-select-dropdown__item.hover,
body.dark .el-select-dropdown__item:hover {
  background-color: var(--bg-tertiary);
}

body.dark .el-select-dropdown__item.selected {
  color: var(--primary-color);
  background-color: var(--bg-tertiary);
}

body.dark .el-popper.is-light {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
}

body.dark .el-popper.is-light .el-popper__arrow::before {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
}

/* 自定义Stories页面中的select组件 */
body.dark .stories-filters .el-select .el-input__wrapper {
  background-color: var(--bg-secondary);
  box-shadow: 0 0 0 1px var(--border-color) inset;
}

body.dark .stories-filters .el-select:hover .el-input__wrapper {
  box-shadow: 0 0 0 1px var(--border-color-hover) inset;
}

body.dark .stories-filters .el-select .el-input__wrapper.is-focus {
  box-shadow: 0 0 0 1px var(--primary-color) inset !important;
}

body.dark .stories-filters .search-input .el-input__wrapper {
  background-color: var(--bg-secondary);
  box-shadow: 0 0 0 1px var(--border-color) inset;
}

body.dark .stories-filters .search-input:hover .el-input__wrapper {
  box-shadow: 0 0 0 1px var(--border-color-hover) inset;
}

body.dark .stories-filters .search-input .el-input__wrapper.is-focus {
  box-shadow: 0 0 0 1px var(--primary-color) inset !important;
} 

body.dark .markdown-content p {
  color: var(--text-primary);
  background-color: var(--bg-tertiary);
  border-radius: 8px;
  padding: 10px;
  box-sizing: border-box;
}


/* 暗黑模式下的 Markdown 样式 */
body.dark .markdown-content {
  color: #e5e7eb;
}

body.dark .markdown-content blockquote {
  color: #9ca3af;
  border-left-color: #4b5563;
}

body.dark .markdown-content pre {
  background-color: #1e293b;
}

body.dark .markdown-content code {
  background-color: #1e293b;
}

body.dark .markdown-content a {
  color: #60a5fa;
}

body.dark .markdown-content table th {
  background-color: #1e293b;
}

body.dark .markdown-content table th,
body.dark .markdown-content table td {
  border-color: #4b5563;
}

body.dark .markdown-content table tr:nth-child(2n) {
  background-color: #1e293b;
}

body.dark .markdown-content hr {
  background-color: #4b5563;
}
